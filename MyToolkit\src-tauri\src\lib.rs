use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::sync::Mutex;
use tauri::State;
use base64::{Engine as _, engine::general_purpose};

// 全局状态存储
type StateStorage = Mutex<HashMap<String, String>>;

// Tauri命令：获取应用版本
#[tauri::command]
fn get_app_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

// Tauri命令：获取应用名称
#[tauri::command]
fn get_app_name() -> String {
    "MyToolkit".to_string()
}

// Tauri命令：保存工具状态
#[tauri::command]
fn save_tool_state(
    tool_id: String,
    state_data: String,
    state: State<StateStorage>,
) -> Result<(), String> {
    let mut storage = state.lock().map_err(|e| e.to_string())?;
    storage.insert(tool_id, state_data);
    Ok(())
}

// Tauri命令：加载工具状态
#[tauri::command]
fn load_tool_state(tool_id: String, state: State<StateStorage>) -> Result<Option<String>, String> {
    let storage = state.lock().map_err(|e| e.to_string())?;
    Ok(storage.get(&tool_id).cloned())
}

// Tauri命令：读取文件内容
#[tauri::command]
fn read_file_content(file_path: String) -> Result<String, String> {
    fs::read_to_string(&file_path).map_err(|e| format!("读取文件失败: {}", e))
}

// Tauri命令：写入文件内容
#[tauri::command]
fn write_file_content(file_path: String, content: String) -> Result<(), String> {
    if let Some(parent) = Path::new(&file_path).parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }
    fs::write(&file_path, content).map_err(|e| format!("写入文件失败: {}", e))
}

// Tauri命令：获取文件信息
#[tauri::command]
fn get_file_info(file_path: String) -> Result<serde_json::Value, String> {
    let metadata = fs::metadata(&file_path).map_err(|e| format!("获取文件信息失败: {}", e))?;

    let file_name = Path::new(&file_path)
        .file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_string();

    Ok(serde_json::json!({
        "name": file_name,
        "path": file_path,
        "size": metadata.len(),
        "is_file": metadata.is_file(),
        "is_dir": metadata.is_dir(),
    }))
}

// Tauri命令：读取文件为Base64
#[tauri::command]
fn read_file_as_base64(file_path: String) -> Result<String, String> {
    let bytes = fs::read(&file_path).map_err(|e| format!("读取文件失败: {}", e))?;
    Ok(general_purpose::STANDARD.encode(bytes))
}

// Tauri命令：从Base64保存文件
#[tauri::command]
fn save_file_from_base64(file_path: String, base64_data: String) -> Result<(), String> {
    let bytes = general_purpose::STANDARD.decode(base64_data).map_err(|e| format!("Base64解码失败: {}", e))?;
    if let Some(parent) = Path::new(&file_path).parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }
    fs::write(&file_path, bytes).map_err(|e| format!("保存文件失败: {}", e))
}

// Tauri命令：读取剪贴板文本
#[tauri::command]
async fn read_clipboard_text() -> Result<String, String> {
    use arboard::Clipboard;

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("创建剪贴板管理器失败: {}", e))?;

    clipboard.get_text()
        .map_err(|e| format!("读取剪贴板文本失败: {}", e))
}

// Tauri命令：写入剪贴板文本
#[tauri::command]
async fn write_clipboard_text(text: String) -> Result<(), String> {
    use arboard::Clipboard;

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("创建剪贴板管理器失败: {}", e))?;

    clipboard.set_text(text)
        .map_err(|e| format!("写入剪贴板文本失败: {}", e))
}

// Tauri命令：读取剪贴板图片（返回base64）
#[tauri::command]
async fn read_clipboard_image() -> Result<String, String> {
    use arboard::Clipboard;
    use image::ImageFormat;
    use std::io::Cursor;

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("创建剪贴板管理器失败: {}", e))?;

    // 尝试读取图片
    match clipboard.get_image() {
        Ok(img_data) => {
            // 将图片数据转换为PNG格式的字节
            let mut png_bytes = Vec::new();
            let mut cursor = Cursor::new(&mut png_bytes);

            // 创建动态图像
            let dynamic_img = image::RgbaImage::from_raw(
                img_data.width as u32,
                img_data.height as u32,
                img_data.bytes.into_owned()
            ).ok_or("无法创建图像数据".to_string())?;

            // 保存为PNG格式
            image::DynamicImage::ImageRgba8(dynamic_img)
                .write_to(&mut cursor, ImageFormat::Png)
                .map_err(|e| format!("图像格式转换失败: {}", e))?;

            // 转换为base64
            let base64_data = general_purpose::STANDARD.encode(&png_bytes);
            Ok(format!("data:image/png;base64,{}", base64_data))
        }
        Err(e) => {
            // 如果图片读取失败，尝试读取文本（可能是base64图片）
            match clipboard.get_text() {
                Ok(text) => {
                    if text.starts_with("data:image/") {
                        Ok(text)
                    } else {
                        Err(format!("剪贴板中没有找到图片: {}", e))
                    }
                }
                Err(_) => Err(format!("剪贴板中没有找到图片: {}", e))
            }
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .manage(StateStorage::default())
        .invoke_handler(tauri::generate_handler![
            get_app_version,
            get_app_name,
            save_tool_state,
            load_tool_state,
            read_file_content,
            write_file_content,
            get_file_info,
            read_file_as_base64,
            save_file_from_base64,
            read_clipboard_text,
            write_clipboard_text,
            read_clipboard_image,
        ])
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
