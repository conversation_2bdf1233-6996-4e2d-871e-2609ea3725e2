D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\libmarkup5ever-78f22784cd31acb2.rmeta: D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-4b16214220f91a68\out/generated.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-4b16214220f91a68\out/named_entities.rs

D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\markup5ever-78f22784cd31acb2.d: D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-4b16214220f91a68\out/generated.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-4b16214220f91a68\out/named_entities.rs

D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs:
D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-4b16214220f91a68\out/generated.rs:
D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-4b16214220f91a68\out/named_entities.rs:

# env-dep:OUT_DIR=D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\markup5ever-4b16214220f91a68\\out
