<template>
  <div class="qr-decoder-view">
    <div class="tool-header">
      <div class="tool-title">
        <QrcodeOutlined class="tool-icon" />
        <div class="tool-info">
          <h1>二维码解码工具</h1>
          <p>支持拖拽、点击选择和剪贴板粘贴二维码图片</p>
        </div>
      </div>

      <div class="tool-actions">
        <Button @click="showHistory" :disabled="loading">
          <HistoryOutlined />
          历史记录
        </Button>
        <Button @click="clearAll" :disabled="loading">
          <DeleteOutlined />
          清空
        </Button>
        <Button @click="showHelp">
          <QuestionCircleOutlined />
          帮助
        </Button>
      </div>
    </div>
    
    <div class="tool-content">
      <!-- 上传区域 -->
      <div class="upload-section">
        <Card class="upload-card">
          <div 
            class="upload-area" 
            :class="{ 
              'dragover': isDragOver, 
              'has-image': previewImage,
              'loading': loading 
            }"
            @click="selectFile"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop"
          >
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state">
              <LoadingOutlined :spin="true" class="loading-icon" />
              <h3>正在处理图片...</h3>
              <p>请稍候，正在解码二维码</p>
            </div>
            
            <!-- 图片预览 -->
            <div v-else-if="previewImage" class="preview-state">
              <img :src="previewImage" alt="二维码预览" class="preview-image" />
              <div class="preview-info">
                <p class="file-name">{{ currentFileName }}</p>
                <p class="file-size">{{ currentFileSize }}</p>
              </div>
              <div class="upload-buttons">
                <Button type="primary" @click.stop="selectFile">
                  <FolderOpenOutlined />
                  重新选择
                </Button>
                <Button @click.stop="clearImage">
                  <DeleteOutlined />
                  清除图片
                </Button>
              </div>
            </div>
            
            <!-- 默认上传状态 -->
            <div v-else class="upload-state">
              <CloudUploadOutlined class="upload-icon" />
              <h3>上传二维码图片</h3>
              <p>拖拽图片到此处，或点击选择文件<br>也可以使用 Ctrl+V 粘贴剪贴板中的图片</p>
              <div class="upload-buttons">
                <Button type="primary">
                  <FolderOpenOutlined />
                  选择文件
                </Button>
                <Button @click.stop="pasteFromClipboard">
                  <CopyOutlined />
                  从剪贴板粘贴
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>
      
      <!-- 结果区域 -->
      <div class="result-section">
        <Card class="result-card">
          <template #title>
            <div class="result-title">
              <FileTextOutlined />
              解码结果
              <Tag v-if="decodeStatus" :color="getStatusColor(decodeStatus)" class="status-tag">
                {{ getStatusText(decodeStatus) }}
              </Tag>
            </div>
          </template>
          
          <template #extra>
            <Space>
              <Button
                type="dashed"
                @click="copyResult"
                :disabled="!result"
                class="copy-button"
              >
                <!-- <CopyOutlined v-if="!copyLoading" />使用这个会影响布局，去掉 -->
                复制结果
              </Button>
              <Button
                @click="saveResult"
                :disabled="!result || loading"
                class="save-button"
              >
                <DownloadOutlined />
                保存文件
              </Button>
            </Space>
          </template>

          <div v-if="!result && !loading" class="empty-result">
            <SearchOutlined class="empty-icon" />
            <p>请上传二维码图片进行解码</p>
          </div>
          
          <div v-else-if="result" class="result-content">
            <TextArea
              v-model:value="result"
              :rows="8"
              readonly
              placeholder="解码结果将显示在这里..."
              class="result-textarea"
            />
            <div class="result-meta">
              <Text type="secondary">
                字符数：{{ result.length }} | 
                解码时间：{{ decodeTime }}ms
              </Text>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 历史记录弹窗 -->
    <Modal
      v-model:open="historyVisible"
      title="解码历史记录"
      width="800px"
      :footer="null"
    >
      <div class="history-content">
        <div v-if="historyList.length === 0" class="empty-history">
          <SearchOutlined class="empty-icon" />
          <p>暂无历史记录</p>
        </div>
        <div v-else class="history-list">
          <div
            v-for="(item, index) in historyList"
            :key="index"
            class="history-item"
          >
            <div class="history-info">
              <div class="history-content-text">{{ item.content }}</div>
              <div class="history-meta">
                <Text type="secondary">
                  {{ formatDate(item.timestamp) }} |
                  字符数：{{ item.content.length }}
                </Text>
              </div>
            </div>
            <div class="history-actions">
              <Button size="small" @click="copyHistoryItem(item.content)">
                <CopyOutlined />
                复制
              </Button>
              <Button size="small" @click="useHistoryItem(item.content)">
                <ImportOutlined />
                使用
              </Button>
              <Button size="small" danger @click="deleteHistoryItem(index)">
                <DeleteOutlined />
                删除
              </Button>
            </div>
          </div>
        </div>
        <div class="history-footer">
          <Button @click="clearHistory" danger>
            <DeleteOutlined />
            清空历史记录
          </Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Card, Button, Input, message, Tag, Space, Typography, Modal } from 'ant-design-vue'
import {
  QrcodeOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  FolderOpenOutlined,
  SearchOutlined,
  LoadingOutlined,
  FileTextOutlined,
  DownloadOutlined,
  HistoryOutlined,
  ImportOutlined
} from '@ant-design/icons-vue'
import { 
  decodeQRCodeFromFile, 
  decodeQRCodeFromClipboard, 
  validateImageFile,
  formatFileSize,
  generateFileName,
  type QRDecodeResult 
} from '@/utils/qrcode'
import { writeClipboardText, saveToolState, loadToolState } from '@/utils/tauri'

const { TextArea, Text } = Input

// 历史记录接口
interface HistoryItem {
  content: string
  timestamp: number
}

// 响应式数据
const result = ref('')
const loading = ref(false)
const copyLoading = ref(false)
const isDragOver = ref(false)
const previewImage = ref('')
const currentFileName = ref('')
const currentFileSize = ref('')
const decodeStatus = ref<'success' | 'error' | 'processing' | ''>('')
const decodeTime = ref(0)
const fileInput = ref<HTMLInputElement>()

// 历史记录相关
const historyVisible = ref(false)
const historyList = ref<HistoryItem[]>([])

// 状态管理函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'success': return 'green'
    case 'error': return 'red'
    case 'processing': return 'orange'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '解码成功'
    case 'error': return '解码失败'
    case 'processing': return '解码中'
    default: return '就绪'
  }
}

// 文件处理函数
const selectFile = () => {
  if (loading.value) return
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

const processFile = async (file: File) => {
  // 验证文件
  const validation = validateImageFile(file)
  if (!validation.valid) {
    message.error(validation.error)
    return
  }

  loading.value = true
  decodeStatus.value = 'processing'
  
  try {
    // 显示文件信息
    currentFileName.value = file.name
    currentFileSize.value = formatFileSize(file.size)
    
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      previewImage.value = e.target?.result as string
    }
    reader.readAsDataURL(file)

    // 解码二维码
    const startTime = Date.now()
    const decodeResult = await decodeQRCodeFromFile(file)
    decodeTime.value = Date.now() - startTime

    handleDecodeResult(decodeResult)
  } catch (error) {
    console.error('处理文件失败:', error)
    message.error('处理文件失败')
    decodeStatus.value = 'error'
  } finally {
    loading.value = false
  }
}

const handleDecodeResult = (decodeResult: QRDecodeResult) => {
  if (decodeResult.success && decodeResult.data) {
    result.value = decodeResult.data
    decodeStatus.value = 'success'
    message.success('二维码解码成功')

    // 添加到历史记录
    addToHistory(decodeResult.data)
  } else {
    result.value = ''
    decodeStatus.value = 'error'
    message.error(decodeResult.error || '解码失败')
  }
}

// 拖拽处理函数
const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

// 剪贴板处理函数
const pasteFromClipboard = async () => {
  if (loading.value) return

  loading.value = true
  decodeStatus.value = 'processing'

  try {
    const startTime = Date.now()
    const decodeResult = await decodeQRCodeFromClipboard()
    decodeTime.value = Date.now() - startTime

    handleDecodeResult(decodeResult)
  } catch (error) {
    console.error('从剪贴板解码失败:', error)
    message.error('从剪贴板解码失败')
    decodeStatus.value = 'error'
  } finally {
    loading.value = false
  }
}

// 复制结果
const copyResult = async () => {
  if (!result.value || copyLoading.value) return

  copyLoading.value = true
  try {
    await writeClipboardText(result.value)
    message.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  } finally {
    copyLoading.value = false
  }
}

// 保存结果
const saveResult = () => {
  if (!result.value) return

  try {
    const fileName = generateFileName('qr-decode-result', 'txt')
    const blob = new Blob([result.value], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    a.click()
    URL.revokeObjectURL(url)
    message.success('文件保存成功')
  } catch (error) {
    console.error('保存文件失败:', error)
    message.error('保存文件失败')
  }
}

// 清除图片
const clearImage = () => {
  previewImage.value = ''
  currentFileName.value = ''
  currentFileSize.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 清空所有
const clearAll = () => {
  result.value = ''
  decodeStatus.value = ''
  decodeTime.value = 0
  clearImage()
  message.info('已清空')
}

// 显示帮助
const showHelp = () => {
  message.info({
    content: `二维码解码工具使用说明：

1. 支持的操作方式：
   • 拖拽图片到上传区域
   • 点击"选择文件"按钮选择图片
   • 使用 Ctrl+V 粘贴剪贴板中的图片

2. 支持的图片格式：
   • JPG/JPEG、PNG、GIF、BMP、WebP

3. 功能特性：
   • 自动识别二维码内容
   • 一键复制解码结果
   • 保存结果为文本文件
   • 实时状态提示

4. 快捷键：
   • Ctrl+V：粘贴剪贴板图片`,
    duration: 10
  })
}

// 历史记录管理
const addToHistory = (content: string) => {
  // 避免重复添加相同内容
  if (historyList.value.some(item => item.content === content)) {
    return
  }

  const historyItem: HistoryItem = {
    content,
    timestamp: Date.now()
  }

  // 添加到列表开头，最多保留50条记录
  historyList.value.unshift(historyItem)
  if (historyList.value.length > 50) {
    historyList.value = historyList.value.slice(0, 50)
  }

  // 保存到本地存储
  saveHistory()
}

const loadHistory = async () => {
  try {
    const saved = await loadToolState('qr-decoder-history')
    if (saved) {
      historyList.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}

const saveHistory = async () => {
  try {
    await saveToolState('qr-decoder-history', JSON.stringify(historyList.value))
  } catch (error) {
    console.error('保存历史记录失败:', error)
  }
}

const showHistory = () => {
  historyVisible.value = true
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const copyHistoryItem = async (content: string) => {
  try {
    await writeClipboardText(content)
    message.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

const useHistoryItem = (content: string) => {
  result.value = content
  decodeStatus.value = 'success'
  historyVisible.value = false
  message.success('已使用历史记录')
}

const deleteHistoryItem = (index: number) => {
  historyList.value.splice(index, 1)
  saveHistory()
  message.success('删除成功')
}

const clearHistory = () => {
  historyList.value = []
  saveHistory()
  message.success('历史记录已清空')
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'v') {
    event.preventDefault()
    pasteFromClipboard()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  loadHistory()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="less" scoped>
.qr-decoder-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 24px;
  overflow: hidden;
}

.tool-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.tool-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-icon {
  font-size: 32px;
  color: #3b82f6;
}

.tool-info {
  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
  }

  p {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: #64748b;
  }
}

.tool-actions {
  display: flex;
  gap: 12px;
}

.tool-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  overflow: hidden;
}

.upload-section, .result-section {
  display: flex;
  flex-direction: column;
}

.upload-card, .result-card {
  height: 100%;

  :deep(.ant-card-body) {
    height: calc(100% - 57px);
    display: flex;
    flex-direction: column;
  }
}

.upload-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;

  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  &.dragover {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: scale(1.02);
  }

  &.loading {
    cursor: not-allowed;
    border-color: #fbbf24;
    background: #fef3c7;
  }

  &.has-image {
    padding: 20px;
    justify-content: flex-start;
  }
}

.loading-state, .upload-state, .preview-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.loading-icon, .upload-icon {
  font-size: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.loading-icon {
  color: #fbbf24;
}

.upload-area h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1e293b;
}

.upload-area p {
  margin: 0 0 24px 0;
  color: #64748b;
  line-height: 1.5;
}

.upload-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;

  :deep(.ant-btn) {
    min-width: 120px;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.preview-info {
  margin-bottom: 16px;
  text-align: center;

  .file-name {
    font-weight: 500;
    color: #1e293b;
    margin: 0 0 4px 0;
    word-break: break-all;
  }

  .file-size {
    font-size: 12px;
    color: #64748b;
    margin: 0;
  }
}

.result-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .status-tag {
    margin-left: 8px;
  }
}

.empty-result {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-textarea {
  flex: 1;
  resize: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.result-meta {
  margin-top: 12px;
  text-align: right;
}

.copy-button {
  min-width: 100px !important;

  :deep(.ant-btn-loading-icon) {
    margin-right: 8px;
  }
}

.save-button {
  min-width: 100px !important;
}

// 历史记录样式
.history-content {
  max-height: 500px;
  overflow-y: auto;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #94a3b8;
  text-align: center;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  transition: all 0.2s;

  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }
}

.history-info {
  flex: 1;
  margin-right: 16px;
}

.history-content-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #1e293b;
  margin-bottom: 8px;
  word-break: break-all;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.history-meta {
  font-size: 12px;
}

.history-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.history-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .tool-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .upload-area {
    padding: 20px;
  }

  .upload-buttons {
    flex-direction: column;
    width: 100%;

    :deep(.ant-btn) {
      width: 100%;
    }
  }

  .history-item {
    flex-direction: column;
    gap: 12px;
  }

  .history-info {
    margin-right: 0;
  }

  .history-actions {
    align-self: flex-end;
  }
}
</style>
