<template>
  <div class="home-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <ToolSidebar />
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- Tab栏 -->
      <div class="tab-bar" v-if="appStore.openTabs.length > 0">
        <Tabs
          v-model:activeKey="appStore.activeTab"
          type="editable-card"
          @edit="handleTabEdit"
          @tabClick="handleTabClick"
        >
          <TabPane
            v-for="toolId in appStore.openTabs"
            :key="toolId"
            :tab="getToolName(toolId)"
            :closable="true"
          />
        </Tabs>
      </div>
      
      <!-- 工具内容区 -->
      <div class="tool-content">
        <router-view v-if="appStore.activeTab" />
        <EmptyState v-else />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tabs, TabPane } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import ToolSidebar from '@/components/ToolSidebar.vue'
import EmptyState from '@/components/EmptyState.vue'

const appStore = useAppStore()
const router = useRouter()

const getToolName = (toolId: string) => {
  const tool = appStore.getToolById(toolId)
  return tool?.name || toolId
}

const handleTabEdit = (targetKey: string, action: 'add' | 'remove') => {
  if (action === 'remove') {
    appStore.closeTool(targetKey)
  }
}

const handleTabClick = (key: string) => {
  appStore.switchTab(key)

  // 路由跳转到子路由
  const tool = appStore.getToolById(key)
  if (tool) {
    // 使用子路由路径，去掉开头的斜杠
    const childRoute = tool.route.startsWith('/') ? tool.route.substring(1) : tool.route
    router.push(`/${childRoute}`)
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.home-container {
  height: 100%;
  display: flex;
  background: @background-color;
  position: relative;
}

.sidebar {
  width: @sidebar-width;
  border-right: 1px solid @border-color;
  background: @background-color-light;
  flex-shrink: 0;
  transition: transform @transition-normal;
  z-index: 100;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0; // 防止flex子元素溢出
}

.tab-bar {
  background: @background-color-dark;
  border-bottom: 1px solid @border-color;
  padding: 0 @spacing-lg;
  min-height: @tab-height;

  :deep(.ant-tabs) {
    margin: 0;

    .ant-tabs-nav {
      margin: 0;

      &::before {
        border: none;
      }
    }

    .ant-tabs-tab {
      background: @background-color;
      border: 1px solid @border-color;
      border-bottom: none;
      border-radius: @border-radius-md @border-radius-md 0 0;
      margin-right: @spacing-xs;
      transition: all @transition-normal;
      position: relative;

      &:hover {
        background: @background-color-light;
        transform: translateY(-1px);
        box-shadow: @box-shadow-sm;
      }

      &.ant-tabs-tab-active {
        background: @background-color;
        border-color: @primary-color;
        z-index: 1;

        .ant-tabs-tab-btn {
          color: @primary-color;
          font-weight: 500;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          height: 2px;
          background: @primary-color;
        }
      }

      .ant-tabs-tab-remove {
        margin-left: @spacing-sm;
        color: @text-color-secondary;
        transition: color @transition-fast;

        &:hover {
          color: @error-color;
        }
      }
    }
  }
}

.tool-content {
  flex: 1;
  overflow: hidden;
  background: @background-color;
  position: relative;
}

// 响应式设计
@media (max-width: 768px) {
  .home-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid @border-color;
    position: fixed;
    top: @titlebar-height;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateX(-100%);

    &.mobile-open {
      transform: translateX(0);
    }
  }

  .main-content {
    flex: 1;
    margin-top: 0;
  }

  .tab-bar {
    padding: 0 @spacing-md;

    :deep(.ant-tabs-tab) {
      margin-right: @spacing-xs;
      font-size: @font-size-xs;
    }
  }
}

@media (max-width: 480px) {
  .tab-bar {
    padding: 0 @spacing-sm;

    :deep(.ant-tabs-tab) {
      padding: @spacing-xs @spacing-sm;

      .ant-tabs-tab-btn {
        font-size: @font-size-xs;
      }
    }
  }
}

// 平板适配
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }
}

// 大屏适配
@media (min-width: 1440px) {
  .sidebar {
    width: 320px;
  }
}
</style>
