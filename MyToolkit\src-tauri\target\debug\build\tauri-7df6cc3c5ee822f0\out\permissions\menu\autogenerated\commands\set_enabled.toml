# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-enabled"
description = "Enables the set_enabled command without any pre-configured scope."
commands.allow = ["set_enabled"]

[[permission]]
identifier = "deny-set-enabled"
description = "Denies the set_enabled command without any pre-configured scope."
commands.deny = ["set_enabled"]
