# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-closable"
description = "Enables the set_closable command without any pre-configured scope."
commands.allow = ["set_closable"]

[[permission]]
identifier = "deny-set-closable"
description = "Denies the set_closable command without any pre-configured scope."
commands.deny = ["set_closable"]
