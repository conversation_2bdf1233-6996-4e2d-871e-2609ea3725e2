["\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\tauri-7df6cc3c5ee822f0\\out\\permissions\\menu\\autogenerated\\default.toml"]