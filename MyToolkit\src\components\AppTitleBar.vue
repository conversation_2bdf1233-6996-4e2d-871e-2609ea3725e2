<template>
  <div class="title-bar" data-tauri-drag-region>
    <!-- 窗口控制按钮 -->
    <div class="window-controls">
      <div class="control-btn close" @click="handleClose"></div>
      <div class="control-btn minimize" @click="handleMinimize"></div>
      <div class="control-btn maximize" @click="handleMaximize"></div>
    </div>
    
    <!-- 应用标题 -->
    <div class="app-title">
      <ToolOutlined class="app-icon" />
      <span class="title-text">{{ appStore.appName || 'MyToolkit' }}</span>
    </div>
    
    <!-- 版本信息 -->
    <div class="app-version">
      v{{ appStore.appVersion }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ToolOutlined } from '@ant-design/icons-vue'
import { useAppStore } from '@/stores/app'
// getCurrentWebviewWindow is auto-imported via vite.config.ts

const appStore = useAppStore()

const handleClose = async () => {
  const appWindow = getCurrentWebviewWindow()
  await appWindow.close()
}

const handleMinimize = async () => {
  const appWindow = getCurrentWebviewWindow()
  await appWindow.minimize()
}

const handleMaximize = async () => {
  const appWindow = getCurrentWebviewWindow()
  await appWindow.toggleMaximize()
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.title-bar {
  height: @titlebar-height;
  background: linear-gradient(90deg, @background-color-light 0%, @border-color-light 100%);
  border-bottom: 1px solid @border-color;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 @spacing-lg;
  user-select: none;
  position: relative;
  backdrop-filter: blur(10px);
}

.window-controls {
  display: flex;
  gap: @spacing-sm;
  z-index: 10;
}

.control-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all @transition-normal;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    opacity: 0;
    transition: opacity @transition-normal;
  }

  &:hover::before {
    opacity: 1;
  }

  &.close {
    background: #ff5f57;

    &:hover {
      background: #ff3b30;
    }

    &::before {
      background: #8b0000;
    }
  }

  &.minimize {
    background: #ffbd2e;

    &:hover {
      background: #ff9500;
    }

    &::before {
      background: #8b4513;
    }
  }

  &.maximize {
    background: #28ca42;

    &:hover {
      background: #30d158;
    }

    &::before {
      background: #006400;
    }
  }
}

.app-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: @spacing-sm;
  font-weight: 600;
  color: @text-color;
}

.app-icon {
  font-size: @font-size-lg;
  color: @primary-color;
}

.title-text {
  font-size: @font-size-sm;
}

.app-version {
  font-size: @font-size-xs;
  color: @text-color-secondary;
  font-weight: 500;
  padding: @spacing-xs @spacing-sm;
  background: rgba(59, 130, 246, 0.1);
  border-radius: @border-radius-sm;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

// 响应式适配
@media (max-width: 768px) {
  .title-bar {
    padding: 0 @spacing-md;
  }

  .app-title {
    .title-text {
      display: none;
    }
  }
}
</style>
