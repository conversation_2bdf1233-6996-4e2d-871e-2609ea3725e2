<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本格式化工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .format-selector {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .format-btn {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .format-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .format-btn:hover:not(.active) {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            overflow: hidden;
        }
        
        .editor-panel {
            display: flex;
            flex-direction: column;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .panel-header {
            background: white;
            padding: 12px 16px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .panel-title {
            font-weight: 500;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .panel-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .editor-textarea {
            flex: 1;
            border: none;
            outline: none;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            background: transparent;
        }
        
        .editor-textarea::placeholder {
            color: #94a3b8;
        }
        
        .format-options {
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 12px 16px;
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .option-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .option-label {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }
        
        .option-input {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
            width: 60px;
        }
        
        .option-checkbox {
            width: 16px;
            height: 16px;
        }
        
        .status-bar {
            background: #f1f5f9;
            padding: 8px 16px;
            font-size: 12px;
            color: #64748b;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .error-message {
            color: #ef4444;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 8px 16px;
            font-size: 12px;
            display: none;
        }
        
        .success-message {
            color: #10b981;
            background: #ecfdf5;
            border: 1px solid #a7f3d0;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 8px 16px;
            font-size: 12px;
            display: none;
        }
        
        .line-numbers {
            background: #f1f5f9;
            border-right: 1px solid #e2e8f0;
            padding: 16px 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #94a3b8;
            user-select: none;
            min-width: 40px;
            text-align: right;
        }
        
        .editor-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title" style="display: flex; align-items: center; gap: 12px;">
                <div class="tool-icon">
                    <i class="fas fa-align-left"></i>
                </div>
                <div>
                    <h1 style="margin: 0; font-size: 24px; font-weight: 600;">文本格式化工具</h1>
                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">JSON、XML、HTML格式化美化</p>
                </div>
            </div>
        </div>
        
        <!-- 格式选择器 -->
        <div class="format-selector">
            <button class="format-btn active" data-format="json" onclick="switchFormat('json')">
                <i class="fas fa-code"></i> JSON
            </button>
            <button class="format-btn" data-format="xml" onclick="switchFormat('xml')">
                <i class="fas fa-file-code"></i> XML
            </button>
            <button class="format-btn" data-format="html" onclick="switchFormat('html')">
                <i class="fas fa-globe"></i> HTML
            </button>
            <button class="format-btn" data-format="css" onclick="switchFormat('css')">
                <i class="fas fa-paint-brush"></i> CSS
            </button>
            <button class="format-btn" data-format="sql" onclick="switchFormat('sql')">
                <i class="fas fa-database"></i> SQL
            </button>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 输入面板 -->
            <div class="editor-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="fas fa-edit"></i>
                        输入内容
                    </div>
                    <div class="panel-actions">
                        <button class="action-btn" onclick="clearInput()">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                        <button class="action-btn" onclick="pasteFromClipboard()">
                            <i class="fas fa-paste"></i> 粘贴
                        </button>
                        <button class="action-btn" onclick="loadSample()">
                            <i class="fas fa-file-import"></i> 示例
                        </button>
                    </div>
                </div>
                
                <div class="error-message" id="input-error"></div>
                
                <div class="editor-content">
                    <div class="line-numbers" id="input-line-numbers">1</div>
                    <textarea 
                        class="editor-textarea" 
                        id="input-text" 
                        placeholder="在此输入要格式化的内容..."
                        oninput="updateLineNumbers('input'); formatText()"
                    ></textarea>
                </div>
                
                <div class="format-options" id="format-options">
                    <!-- 动态生成格式选项 -->
                </div>
                
                <div class="status-bar">
                    <span id="input-stats">字符: 0 | 行数: 1</span>
                    <span id="format-status">就绪</span>
                </div>
            </div>
            
            <!-- 输出面板 -->
            <div class="editor-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="fas fa-check-circle"></i>
                        格式化结果
                    </div>
                    <div class="panel-actions">
                        <button class="action-btn primary" onclick="copyOutput()">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                        <button class="action-btn" onclick="downloadOutput()">
                            <i class="fas fa-download"></i> 下载
                        </button>
                        <button class="action-btn" onclick="validateOutput()">
                            <i class="fas fa-check"></i> 验证
                        </button>
                    </div>
                </div>
                
                <div class="success-message" id="output-success"></div>
                <div class="error-message" id="output-error"></div>
                
                <div class="editor-content">
                    <div class="line-numbers" id="output-line-numbers">1</div>
                    <textarea 
                        class="editor-textarea" 
                        id="output-text" 
                        readonly
                        placeholder="格式化结果将显示在这里..."
                    ></textarea>
                </div>
                
                <div class="status-bar">
                    <span id="output-stats">字符: 0 | 行数: 1</span>
                    <span id="validation-status">未验证</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentFormat = 'json';
        
        // 格式配置
        const formatConfigs = {
            json: {
                name: 'JSON',
                placeholder: '输入JSON字符串...',
                sample: '{"name":"张三","age":25,"city":"北京","hobbies":["读书","旅行","编程"]}',
                options: [
                    { type: 'number', id: 'indent', label: '缩进空格', value: 2, min: 1, max: 8 },
                    { type: 'checkbox', id: 'sortKeys', label: '排序键名', checked: false }
                ]
            },
            xml: {
                name: 'XML',
                placeholder: '输入XML字符串...',
                sample: '<person><name>张三</name><age>25</age><city>北京</city></person>',
                options: [
                    { type: 'number', id: 'indent', label: '缩进空格', value: 2, min: 1, max: 8 },
                    { type: 'checkbox', id: 'selfClose', label: '自闭合标签', checked: true }
                ]
            },
            html: {
                name: 'HTML',
                placeholder: '输入HTML代码...',
                sample: '<div class="container"><h1>标题</h1><p>这是一段文本内容。</p></div>',
                options: [
                    { type: 'number', id: 'indent', label: '缩进空格', value: 2, min: 1, max: 8 },
                    { type: 'checkbox', id: 'wrapAttributes', label: '换行属性', checked: false }
                ]
            },
            css: {
                name: 'CSS',
                placeholder: '输入CSS代码...',
                sample: '.container{margin:0 auto;padding:20px;background:#fff}.title{font-size:24px;color:#333}',
                options: [
                    { type: 'number', id: 'indent', label: '缩进空格', value: 2, min: 1, max: 8 },
                    { type: 'checkbox', id: 'expandShorthand', label: '展开简写', checked: false }
                ]
            },
            sql: {
                name: 'SQL',
                placeholder: '输入SQL语句...',
                sample: 'SELECT u.name,u.email,p.title FROM users u LEFT JOIN posts p ON u.id=p.user_id WHERE u.active=1 ORDER BY u.created_at DESC',
                options: [
                    { type: 'number', id: 'indent', label: '缩进空格', value: 2, min: 1, max: 8 },
                    { type: 'checkbox', id: 'uppercase', label: '关键字大写', checked: true }
                ]
            }
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            switchFormat('json');
            updateLineNumbers('input');
            updateLineNumbers('output');
        });
        
        // 切换格式
        function switchFormat(format) {
            currentFormat = format;
            
            // 更新按钮状态
            document.querySelectorAll('.format-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-format="${format}"]`).classList.add('active');
            
            // 更新配置
            const config = formatConfigs[format];
            document.getElementById('input-text').placeholder = config.placeholder;
            
            // 生成格式选项
            generateFormatOptions(config.options);
            
            // 重新格式化
            formatText();
        }
        
        // 生成格式选项
        function generateFormatOptions(options) {
            const container = document.getElementById('format-options');
            container.innerHTML = '';
            
            options.forEach(option => {
                const group = document.createElement('div');
                group.className = 'option-group';
                
                if (option.type === 'number') {
                    group.innerHTML = `
                        <label class="option-label">${option.label}:</label>
                        <input type="number" class="option-input" id="${option.id}" 
                               value="${option.value}" min="${option.min}" max="${option.max}"
                               onchange="formatText()">
                    `;
                } else if (option.type === 'checkbox') {
                    group.innerHTML = `
                        <input type="checkbox" class="option-checkbox" id="${option.id}" 
                               ${option.checked ? 'checked' : ''} onchange="formatText()">
                        <label class="option-label" for="${option.id}">${option.label}</label>
                    `;
                }
                
                container.appendChild(group);
            });
        }
        
        // 格式化文本
        function formatText() {
            const input = document.getElementById('input-text').value.trim();
            const outputElement = document.getElementById('output-text');
            const inputError = document.getElementById('input-error');
            const outputError = document.getElementById('output-error');
            
            // 清除错误信息
            inputError.style.display = 'none';
            outputError.style.display = 'none';
            
            if (!input) {
                outputElement.value = '';
                updateStats();
                updateLineNumbers('output');
                return;
            }
            
            try {
                let formatted = '';
                
                switch (currentFormat) {
                    case 'json':
                        formatted = formatJSON(input);
                        break;
                    case 'xml':
                        formatted = formatXML(input);
                        break;
                    case 'html':
                        formatted = formatHTML(input);
                        break;
                    case 'css':
                        formatted = formatCSS(input);
                        break;
                    case 'sql':
                        formatted = formatSQL(input);
                        break;
                }
                
                outputElement.value = formatted;
                document.getElementById('format-status').textContent = '格式化成功';
                
            } catch (error) {
                inputError.textContent = `格式化错误: ${error.message}`;
                inputError.style.display = 'block';
                outputElement.value = '';
                document.getElementById('format-status').textContent = '格式化失败';
            }
            
            updateStats();
            updateLineNumbers('output');
        }
        
        // JSON格式化
        function formatJSON(input) {
            const indent = parseInt(document.getElementById('indent').value);
            const sortKeys = document.getElementById('sortKeys').checked;
            
            let parsed = JSON.parse(input);
            
            if (sortKeys && typeof parsed === 'object' && parsed !== null) {
                parsed = sortObjectKeys(parsed);
            }
            
            return JSON.stringify(parsed, null, indent);
        }
        
        // 递归排序对象键
        function sortObjectKeys(obj) {
            if (Array.isArray(obj)) {
                return obj.map(sortObjectKeys);
            } else if (typeof obj === 'object' && obj !== null) {
                const sorted = {};
                Object.keys(obj).sort().forEach(key => {
                    sorted[key] = sortObjectKeys(obj[key]);
                });
                return sorted;
            }
            return obj;
        }
        
        // XML格式化
        function formatXML(input) {
            const indent = parseInt(document.getElementById('indent').value);
            const parser = new DOMParser();
            const doc = parser.parseFromString(input, 'text/xml');
            
            if (doc.querySelector('parsererror')) {
                throw new Error('XML格式错误');
            }
            
            return formatXMLNode(doc.documentElement, 0, indent);
        }
        
        // 格式化XML节点
        function formatXMLNode(node, level, indent) {
            const indentStr = ' '.repeat(level * indent);
            let result = '';
            
            if (node.nodeType === Node.ELEMENT_NODE) {
                result += indentStr + '<' + node.nodeName;
                
                // 添加属性
                for (let attr of node.attributes) {
                    result += ` ${attr.name}="${attr.value}"`;
                }
                
                if (node.childNodes.length === 0) {
                    result += '/>\n';
                } else {
                    result += '>\n';
                    
                    for (let child of node.childNodes) {
                        if (child.nodeType === Node.TEXT_NODE) {
                            const text = child.textContent.trim();
                            if (text) {
                                result += indentStr + ' '.repeat(indent) + text + '\n';
                            }
                        } else if (child.nodeType === Node.ELEMENT_NODE) {
                            result += formatXMLNode(child, level + 1, indent);
                        }
                    }
                    
                    result += indentStr + '</' + node.nodeName + '>\n';
                }
            }
            
            return result;
        }
        
        // HTML格式化
        function formatHTML(input) {
            const indent = parseInt(document.getElementById('indent').value);
            // 简单的HTML格式化实现
            return input.replace(/></g, '>\n<')
                       .split('\n')
                       .map((line, index) => {
                           const trimmed = line.trim();
                           if (!trimmed) return '';
                           
                           let level = 0;
                           const openTags = (input.substring(0, input.indexOf(trimmed)).match(/</g) || []).length;
                           const closeTags = (input.substring(0, input.indexOf(trimmed)).match(/\//g) || []).length;
                           level = Math.max(0, openTags - closeTags);
                           
                           return ' '.repeat(level * indent) + trimmed;
                       })
                       .filter(line => line.trim())
                       .join('\n');
        }
        
        // CSS格式化
        function formatCSS(input) {
            const indent = parseInt(document.getElementById('indent').value);
            const indentStr = ' '.repeat(indent);
            
            return input
                .replace(/\s*{\s*/g, ' {\n')
                .replace(/;\s*/g, ';\n')
                .replace(/\s*}\s*/g, '\n}\n')
                .split('\n')
                .map(line => {
                    const trimmed = line.trim();
                    if (!trimmed) return '';
                    
                    if (trimmed.endsWith('{') || trimmed === '}') {
                        return trimmed;
                    } else {
                        return indentStr + trimmed;
                    }
                })
                .filter(line => line.trim())
                .join('\n');
        }
        
        // SQL格式化
        function formatSQL(input) {
            const indent = parseInt(document.getElementById('indent').value);
            const uppercase = document.getElementById('uppercase').checked;
            const indentStr = ' '.repeat(indent);
            
            let formatted = input;
            
            if (uppercase) {
                const keywords = ['SELECT', 'FROM', 'WHERE', 'JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'INNER JOIN', 
                                'ORDER BY', 'GROUP BY', 'HAVING', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP'];
                
                keywords.forEach(keyword => {
                    const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
                    formatted = formatted.replace(regex, keyword);
                });
            }
            
            // 简单的SQL格式化
            formatted = formatted
                .replace(/\s+(SELECT|FROM|WHERE|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|ORDER BY|GROUP BY|HAVING)\s+/gi, '\n$1 ')
                .replace(/,\s*/g, ',\n' + indentStr)
                .split('\n')
                .map(line => line.trim())
                .filter(line => line)
                .join('\n');
            
            return formatted;
        }
        
        // 更新行号
        function updateLineNumbers(type) {
            const textarea = document.getElementById(`${type}-text`);
            const lineNumbers = document.getElementById(`${type}-line-numbers`);
            
            const lines = textarea.value.split('\n').length;
            const numbers = Array.from({length: lines}, (_, i) => i + 1).join('\n');
            lineNumbers.textContent = numbers;
        }
        
        // 更新统计信息
        function updateStats() {
            const inputText = document.getElementById('input-text').value;
            const outputText = document.getElementById('output-text').value;
            
            document.getElementById('input-stats').textContent = 
                `字符: ${inputText.length} | 行数: ${inputText.split('\n').length}`;
            
            document.getElementById('output-stats').textContent = 
                `字符: ${outputText.length} | 行数: ${outputText.split('\n').length}`;
        }
        
        // 清空输入
        function clearInput() {
            document.getElementById('input-text').value = '';
            document.getElementById('output-text').value = '';
            updateStats();
            updateLineNumbers('input');
            updateLineNumbers('output');
        }
        
        // 从剪贴板粘贴
        async function pasteFromClipboard() {
            try {
                const text = await navigator.clipboard.readText();
                document.getElementById('input-text').value = text;
                formatText();
                updateStats();
                updateLineNumbers('input');
            } catch (error) {
                alert('无法访问剪贴板');
            }
        }
        
        // 加载示例
        function loadSample() {
            const config = formatConfigs[currentFormat];
            document.getElementById('input-text').value = config.sample;
            formatText();
            updateStats();
            updateLineNumbers('input');
        }
        
        // 复制输出
        async function copyOutput() {
            const outputText = document.getElementById('output-text').value;
            if (!outputText) return;
            
            try {
                await navigator.clipboard.writeText(outputText);
                
                const btn = event.target.closest('.action-btn');
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                }, 2000);
                
            } catch (error) {
                alert('复制失败');
            }
        }
        
        // 下载输出
        function downloadOutput() {
            const outputText = document.getElementById('output-text').value;
            if (!outputText) return;
            
            const blob = new Blob([outputText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `formatted-${currentFormat}-${new Date().getTime()}.${currentFormat}`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 验证输出
        function validateOutput() {
            const outputText = document.getElementById('output-text').value;
            const outputSuccess = document.getElementById('output-success');
            const outputError = document.getElementById('output-error');
            
            outputSuccess.style.display = 'none';
            outputError.style.display = 'none';
            
            if (!outputText) return;
            
            try {
                switch (currentFormat) {
                    case 'json':
                        JSON.parse(outputText);
                        break;
                    case 'xml':
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(outputText, 'text/xml');
                        if (doc.querySelector('parsererror')) {
                            throw new Error('XML格式错误');
                        }
                        break;
                    default:
                        // 其他格式暂时跳过验证
                        break;
                }
                
                outputSuccess.textContent = '验证通过：格式正确';
                outputSuccess.style.display = 'block';
                document.getElementById('validation-status').textContent = '验证通过';
                
            } catch (error) {
                outputError.textContent = `验证失败: ${error.message}`;
                outputError.style.display = 'block';
                document.getElementById('validation-status').textContent = '验证失败';
            }
        }
        
        // 监听输入变化
        document.getElementById('input-text').addEventListener('input', function() {
            updateStats();
            updateLineNumbers('input');
        });
        
        document.getElementById('output-text').addEventListener('input', function() {
            updateLineNumbers('output');
        });
    </script>
</body>
</html>
