# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-clear-all-browsing-data"
description = "Enables the clear_all_browsing_data command without any pre-configured scope."
commands.allow = ["clear_all_browsing_data"]

[[permission]]
identifier = "deny-clear-all-browsing-data"
description = "Denies the clear_all_browsing_data command without any pre-configured scope."
commands.deny = ["clear_all_browsing_data"]
