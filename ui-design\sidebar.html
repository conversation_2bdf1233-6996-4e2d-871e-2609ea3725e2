<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8fafc;
            height: 100vh;
            overflow: hidden;
        }
        
        .sidebar {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            background: white;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 10px 12px 10px 40px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: #f8fafc;
            transition: all 0.2s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 14px;
        }
        
        .tool-categories {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }
        
        .category {
            margin-bottom: 24px;
        }
        
        .category-title {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
            padding: 0 8px;
        }
        
        .tool-list {
            space-y: 2px;
        }
        
        .tool-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }
        
        .tool-item:hover {
            background: white;
            border-color: #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transform: translateY(-1px);
        }
        
        .tool-item.active {
            background: #eff6ff;
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .tool-info {
            flex: 1;
        }
        
        .tool-name {
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        .tool-desc {
            font-size: 12px;
            color: #64748b;
            line-height: 1.3;
        }
        
        .tool-badge {
            background: #10b981;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid #e2e8f0;
            background: white;
        }
        
        .footer-info {
            text-align: center;
            font-size: 12px;
            color: #64748b;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 8px;
        }
        
        .footer-link {
            color: #64748b;
            text-decoration: none;
            font-size: 12px;
            transition: color 0.2s;
        }
        
        .footer-link:hover {
            color: #3b82f6;
        }
        
        /* 滚动条样式 */
        .tool-categories::-webkit-scrollbar {
            width: 6px;
        }
        
        .tool-categories::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .tool-categories::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .tool-categories::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="搜索工具..." id="search-input">
            </div>
        </div>
        
        <!-- 工具分类 -->
        <div class="tool-categories" id="tool-categories">
            <!-- 图像处理工具 -->
            <div class="category">
                <div class="category-title">图像处理</div>
                <div class="tool-list">
                    <div class="tool-item" data-tool-id="qr-decoder" data-tool-name="二维码解码" data-tool-url="qr-decoder.html">
                        <div class="tool-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">二维码解码</div>
                            <div class="tool-desc">解析二维码图片内容，支持拖拽和粘贴</div>
                        </div>
                        <div class="tool-badge">NEW</div>
                    </div>
                    
                    <div class="tool-item" data-tool-id="qr-generator" data-tool-name="二维码生成" data-tool-url="qr-generator.html">
                        <div class="tool-icon">
                            <i class="fas fa-plus-square"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">二维码生成</div>
                            <div class="tool-desc">将文本内容生成二维码图片</div>
                        </div>
                    </div>
                    
                    <div class="tool-item" data-tool-id="image-compress" data-tool-name="图片压缩" data-tool-url="image-compress.html">
                        <div class="tool-icon">
                            <i class="fas fa-compress-alt"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">图片压缩</div>
                            <div class="tool-desc">批量压缩图片，减小文件大小</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 文本处理工具 -->
            <div class="category">
                <div class="category-title">文本处理</div>
                <div class="tool-list">
                    <div class="tool-item" data-tool-id="text-formatter" data-tool-name="文本格式化" data-tool-url="text-formatter.html">
                        <div class="tool-icon">
                            <i class="fas fa-align-left"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">文本格式化</div>
                            <div class="tool-desc">JSON、XML、HTML格式化美化</div>
                        </div>
                    </div>
                    
                    <div class="tool-item" data-tool-id="hash-generator" data-tool-name="哈希生成" data-tool-url="hash-generator.html">
                        <div class="tool-icon">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">哈希生成</div>
                            <div class="tool-desc">MD5、SHA1、SHA256等哈希计算</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 开发工具 -->
            <div class="category">
                <div class="category-title">开发工具</div>
                <div class="tool-list">
                    <div class="tool-item" data-tool-id="color-picker" data-tool-name="颜色选择器" data-tool-url="color-picker.html">
                        <div class="tool-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">颜色选择器</div>
                            <div class="tool-desc">RGB、HEX、HSL颜色转换</div>
                        </div>
                    </div>
                    
                    <div class="tool-item" data-tool-id="regex-tester" data-tool-name="正则测试" data-tool-url="regex-tester.html">
                        <div class="tool-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="tool-info">
                            <div class="tool-name">正则测试</div>
                            <div class="tool-desc">正则表达式测试和匹配</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 侧边栏底部 -->
        <div class="sidebar-footer">
            <div class="footer-info">工具箱 v1.0.0</div>
            <div class="footer-links">
                <a href="#" class="footer-link">帮助</a>
                <a href="#" class="footer-link">设置</a>
                <a href="#" class="footer-link">关于</a>
            </div>
        </div>
    </div>

    <script>
        // 工具项点击处理
        document.addEventListener('DOMContentLoaded', function() {
            const toolItems = document.querySelectorAll('.tool-item');
            const searchInput = document.getElementById('search-input');
            
            // 工具项点击事件
            toolItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有活动状态
                    toolItems.forEach(i => i.classList.remove('active'));
                    // 添加当前活动状态
                    this.classList.add('active');
                    
                    // 获取工具信息
                    const toolId = this.dataset.toolId;
                    const toolName = this.dataset.toolName;
                    const toolUrl = this.dataset.toolUrl;
                    
                    // 向父窗口发送消息
                    window.parent.postMessage({
                        type: 'openTool',
                        toolId: toolId,
                        toolName: toolName,
                        toolUrl: toolUrl
                    }, '*');
                });
            });
            
            // 搜索功能
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const categories = document.querySelectorAll('.category');
                
                categories.forEach(category => {
                    const tools = category.querySelectorAll('.tool-item');
                    let hasVisibleTools = false;
                    
                    tools.forEach(tool => {
                        const toolName = tool.querySelector('.tool-name').textContent.toLowerCase();
                        const toolDesc = tool.querySelector('.tool-desc').textContent.toLowerCase();
                        
                        if (toolName.includes(searchTerm) || toolDesc.includes(searchTerm)) {
                            tool.style.display = 'flex';
                            hasVisibleTools = true;
                        } else {
                            tool.style.display = 'none';
                        }
                    });
                    
                    // 如果分类下没有可见的工具，隐藏整个分类
                    category.style.display = hasVisibleTools ? 'block' : 'none';
                });
            });
        });
    </script>
</body>
</html>
