{"name": "my-toolkit", "private": true, "version": "1.0.0", "type": "module", "description": "桌面工具箱应用 - 提供个人常用的小工具集合", "author": "开发团队", "license": "MIT", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tauri-apps/api": "^2.5.0", "ant-design-vue": "^4.0.8", "jsqr": "^1.4.0", "pinia": "^2.1.7", "qrcode": "^1.5.3", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.11.0", "@tauri-apps/cli": "^2.5.0", "@types/node": "^20.10.4", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "less": "^4.2.0", "prettier": "^3.1.0", "typescript": "^5.3.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.5", "vitest": "^1.0.4", "vue-tsc": "^1.8.22"}, "keywords": ["tauri", "vue", "typescript", "desktop-app", "toolkit", "qr-code"]}