# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-minimizable"
description = "Enables the is_minimizable command without any pre-configured scope."
commands.allow = ["is_minimizable"]

[[permission]]
identifier = "deny-is-minimizable"
description = "Denies the is_minimizable command without any pre-configured scope."
commands.deny = ["is_minimizable"]
