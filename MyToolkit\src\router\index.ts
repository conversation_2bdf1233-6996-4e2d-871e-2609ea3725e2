import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: '工具箱',
    },
    children: [
      {
        path: 'qr-decoder',
        name: 'QRDecoder',
        component: () => import('@/views/tools/QRDecoderView.vue'),
        meta: {
          title: '二维码解码',
          toolId: 'qr-decoder',
        },
      },
      {
        path: 'qr-generator',
        name: 'QRGenerator',
        component: () => import('@/views/tools/QRGeneratorView.vue'),
        meta: {
          title: '二维码生成',
          toolId: 'qr-generator',
        },
      },
      {
        path: 'json-formatter',
        name: 'JSONFormatter',
        component: () => import('@/views/tools/JSONFormatterView.vue'),
        meta: {
          title: 'JSON格式化',
          toolId: 'json-formatter',
        },
      },
    ],
  },
  {
    path: '/settings',
    name: 'Setting<PERSON>',
    component: () => import('@/views/SettingsView.vue'),
    meta: {
      title: '设置',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
    },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 工具箱`
  }
  
  next()
})

export default router
