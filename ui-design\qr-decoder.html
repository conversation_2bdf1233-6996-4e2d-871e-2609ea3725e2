<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码解码工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .tool-info h1 {
            font-size: 24px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .tool-info p {
            font-size: 14px;
            color: #64748b;
            margin: 4px 0 0 0;
        }
        
        .tool-actions {
            display: flex;
            gap: 12px;
        }
        
        .action-btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            overflow: hidden;
        }
        
        .upload-section {
            background: #f8fafc;
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .upload-section.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
            transform: scale(1.02);
        }
        
        .upload-section.has-image {
            padding: 20px;
            justify-content: flex-start;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #94a3b8;
            margin-bottom: 16px;
        }
        
        .upload-text h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 8px 0;
        }
        
        .upload-text p {
            font-size: 14px;
            color: #64748b;
            margin: 0 0 16px 0;
        }
        
        .upload-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .upload-btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: white;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 16px;
        }
        
        .result-section {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            display: flex;
            flex-direction: column;
        }
        
        .result-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 16px;
        }
        
        .result-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .result-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .result-text {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #1e293b;
            white-space: pre-wrap;
            word-break: break-all;
            min-height: 120px;
            resize: vertical;
            overflow-y: auto;
        }
        
        .result-actions {
            margin-top: 16px;
            display: flex;
            gap: 12px;
        }
        
        .copy-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .copy-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .copy-btn.copied {
            background: #3b82f6;
        }
        
        .empty-result {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #94a3b8;
            text-align: center;
        }
        
        .empty-result i {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-error {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-processing {
            background: #fef3c7;
            color: #d97706;
        }
        
        #file-input {
            display: none;
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title">
                <div class="tool-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="tool-info">
                    <h1>二维码解码工具</h1>
                    <p>支持拖拽、点击选择和剪贴板粘贴二维码图片</p>
                </div>
            </div>
            <div class="tool-actions">
                <button class="action-btn" onclick="clearAll()">
                    <i class="fas fa-trash"></i>
                    清空
                </button>
                <button class="action-btn" onclick="showHelp()">
                    <i class="fas fa-question-circle"></i>
                    帮助
                </button>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-section" id="upload-section">
                <div class="upload-content" id="upload-content">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <div class="upload-text">
                        <h3>上传二维码图片</h3>
                        <p>拖拽图片到此处，或点击按钮选择文件<br>也可以使用 Ctrl+V 粘贴剪贴板中的图片</p>
                    </div>
                    <div class="upload-buttons">
                        <button class="upload-btn btn-primary" onclick="selectFile()">
                            <i class="fas fa-folder-open"></i>
                            选择文件
                        </button>
                        <button class="upload-btn btn-secondary" onclick="pasteFromClipboard()">
                            <i class="fas fa-paste"></i>
                            从剪贴板粘贴
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 结果区域 -->
            <div class="result-section">
                <div class="result-header">
                    <div class="result-title">
                        <i class="fas fa-file-alt"></i>
                        解码结果
                        <span class="status-indicator" id="status-indicator" style="display: none;">
                            <i class="fas fa-circle"></i>
                            就绪
                        </span>
                    </div>
                </div>
                <div class="result-content">
                    <div class="empty-result" id="empty-result">
                        <i class="fas fa-search"></i>
                        <p>请上传二维码图片进行解码</p>
                    </div>
                    <textarea class="result-text" id="result-text" style="display: none;" readonly placeholder="解码结果将显示在这里..."></textarea>
                    <div class="result-actions" id="result-actions" style="display: none;">
                        <button class="copy-btn" id="copy-btn" onclick="copyResult()">
                            <i class="fas fa-copy"></i>
                            复制结果
                        </button>
                        <button class="action-btn" onclick="saveResult()">
                            <i class="fas fa-download"></i>
                            保存为文件
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <input type="file" id="file-input" accept="image/*" onchange="handleFileSelect(event)">

    <script>
        // 全局变量
        let currentResult = '';
        
        // DOM元素
        const uploadSection = document.getElementById('upload-section');
        const uploadContent = document.getElementById('upload-content');
        const fileInput = document.getElementById('file-input');
        const resultText = document.getElementById('result-text');
        const emptyResult = document.getElementById('empty-result');
        const resultActions = document.getElementById('result-actions');
        const statusIndicator = document.getElementById('status-indicator');
        const copyBtn = document.getElementById('copy-btn');
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupDragAndDrop();
            setupKeyboardShortcuts();
        });
        
        // 设置拖拽功能
        function setupDragAndDrop() {
            uploadSection.addEventListener('dragover', handleDragOver);
            uploadSection.addEventListener('dragleave', handleDragLeave);
            uploadSection.addEventListener('drop', handleDrop);
        }
        
        // 设置键盘快捷键
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'v') {
                    e.preventDefault();
                    pasteFromClipboard();
                }
            });
        }
        
        // 拖拽处理
        function handleDragOver(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        }
        
        function handleDragLeave(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }
        
        // 文件选择
        function selectFile() {
            fileInput.click();
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processFile(file);
            }
        }
        
        // 从剪贴板粘贴
        async function pasteFromClipboard() {
            try {
                const clipboardItems = await navigator.clipboard.read();
                for (const clipboardItem of clipboardItems) {
                    for (const type of clipboardItem.types) {
                        if (type.startsWith('image/')) {
                            const blob = await clipboardItem.getType(type);
                            processFile(blob);
                            return;
                        }
                    }
                }
                showStatus('剪贴板中没有找到图片', 'error');
            } catch (err) {
                showStatus('无法访问剪贴板', 'error');
            }
        }
        
        // 处理文件
        function processFile(file) {
            if (!file.type.startsWith('image/')) {
                showStatus('请选择图片文件', 'error');
                return;
            }
            
            showStatus('正在处理图片...', 'processing');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                displayImage(e.target.result);
                decodeQRCode(e.target.result);
            };
            reader.readAsDataURL(file);
        }
        
        // 显示图片预览
        function displayImage(imageSrc) {
            uploadSection.classList.add('has-image');
            uploadContent.innerHTML = `
                <img src="${imageSrc}" alt="二维码预览" class="preview-image">
                <div class="upload-buttons">
                    <button class="upload-btn btn-primary" onclick="selectFile()">
                        <i class="fas fa-folder-open"></i>
                        重新选择
                    </button>
                    <button class="upload-btn btn-secondary" onclick="clearImage()">
                        <i class="fas fa-times"></i>
                        清除图片
                    </button>
                </div>
            `;
        }
        
        // 解码二维码
        function decodeQRCode(imageSrc) {
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const code = jsQR(imageData.data, imageData.width, imageData.height);
                
                if (code) {
                    showResult(code.data);
                    showStatus('解码成功', 'success');
                } else {
                    showResult('');
                    showStatus('未检测到二维码', 'error');
                }
            };
            img.src = imageSrc;
        }
        
        // 显示结果
        function showResult(result) {
            currentResult = result;
            
            if (result) {
                emptyResult.style.display = 'none';
                resultText.style.display = 'block';
                resultActions.style.display = 'flex';
                resultText.value = result;
            } else {
                emptyResult.style.display = 'flex';
                resultText.style.display = 'none';
                resultActions.style.display = 'none';
                emptyResult.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>未能识别二维码内容</p>
                `;
            }
        }
        
        // 显示状态
        function showStatus(message, type) {
            statusIndicator.style.display = 'inline-flex';
            statusIndicator.className = `status-indicator status-${type}`;
            
            const icon = type === 'success' ? 'check' : type === 'error' ? 'times' : 'clock';
            statusIndicator.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
            
            if (type !== 'processing') {
                setTimeout(() => {
                    statusIndicator.style.display = 'none';
                }, 3000);
            }
        }
        
        // 复制结果
        async function copyResult() {
            if (!currentResult) return;
            
            try {
                await navigator.clipboard.writeText(currentResult);
                copyBtn.classList.add('copied');
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                
                setTimeout(() => {
                    copyBtn.classList.remove('copied');
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制结果';
                }, 2000);
            } catch (err) {
                showStatus('复制失败', 'error');
            }
        }
        
        // 保存结果
        function saveResult() {
            if (!currentResult) return;
            
            const blob = new Blob([currentResult], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `qr-decode-result-${new Date().getTime()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 清除图片
        function clearImage() {
            uploadSection.classList.remove('has-image');
            uploadContent.innerHTML = `
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <div class="upload-text">
                    <h3>上传二维码图片</h3>
                    <p>拖拽图片到此处，或点击按钮选择文件<br>也可以使用 Ctrl+V 粘贴剪贴板中的图片</p>
                </div>
                <div class="upload-buttons">
                    <button class="upload-btn btn-primary" onclick="selectFile()">
                        <i class="fas fa-folder-open"></i>
                        选择文件
                    </button>
                    <button class="upload-btn btn-secondary" onclick="pasteFromClipboard()">
                        <i class="fas fa-paste"></i>
                        从剪贴板粘贴
                    </button>
                </div>
            `;
        }
        
        // 清空所有
        function clearAll() {
            clearImage();
            showResult('');
            emptyResult.innerHTML = `
                <i class="fas fa-search"></i>
                <p>请上传二维码图片进行解码</p>
            `;
            statusIndicator.style.display = 'none';
            fileInput.value = '';
        }
        
        // 显示帮助
        function showHelp() {
            alert(`二维码解码工具使用说明：

1. 支持的操作方式：
   • 拖拽图片到上传区域
   • 点击"选择文件"按钮选择图片
   • 使用 Ctrl+V 粘贴剪贴板中的图片

2. 支持的图片格式：
   • JPG/JPEG
   • PNG
   • GIF
   • BMP
   • WebP

3. 功能特性：
   • 自动识别二维码内容
   • 一键复制解码结果
   • 保存结果为文本文件
   • 实时状态提示

4. 快捷键：
   • Ctrl+V：粘贴剪贴板图片`);
        }
    </script>
</body>
</html>
