<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            overflow: hidden;
        }
        
        .input-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            display: flex;
            flex-direction: column;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .generate-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .generate-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .generate-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        
        .preview-section {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .qr-preview {
            width: 256px;
            height: 256px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            background: white;
        }
        
        .qr-placeholder {
            color: #9ca3af;
            text-align: center;
        }
        
        .qr-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .action-btn {
            padding: 10px 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .action-btn.primary {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .action-btn.primary:hover {
            background: #059669;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .color-input {
            width: 60px;
            height: 40px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title">
                <div class="tool-icon">
                    <i class="fas fa-plus-square"></i>
                </div>
                <div class="tool-info">
                    <h1>二维码生成工具</h1>
                    <p>将文本内容生成为二维码图片</p>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="form-group">
                    <label class="form-label" for="qr-text">要生成的内容 *</label>
                    <textarea 
                        class="form-input form-textarea" 
                        id="qr-text" 
                        placeholder="输入要生成二维码的文本内容..."
                        oninput="updatePreview()"
                    ></textarea>
                </div>
                
                <div class="settings-grid">
                    <div class="form-group">
                        <label class="form-label" for="qr-size">尺寸</label>
                        <select class="form-input form-select" id="qr-size" onchange="updatePreview()">
                            <option value="256">256x256</option>
                            <option value="512">512x512</option>
                            <option value="1024">1024x1024</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="error-level">容错级别</label>
                        <select class="form-input form-select" id="error-level" onchange="updatePreview()">
                            <option value="L">低 (7%)</option>
                            <option value="M" selected>中 (15%)</option>
                            <option value="Q">较高 (25%)</option>
                            <option value="H">高 (30%)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="fg-color">前景色</label>
                        <input type="color" class="color-input" id="fg-color" value="#000000" onchange="updatePreview()">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="bg-color">背景色</label>
                        <input type="color" class="color-input" id="bg-color" value="#ffffff" onchange="updatePreview()">
                    </div>
                </div>
                
                <button class="generate-btn" onclick="generateQR()" id="generate-btn">
                    <i class="fas fa-magic"></i>
                    生成二维码
                </button>
            </div>
            
            <!-- 预览区域 -->
            <div class="preview-section">
                <div class="qr-preview" id="qr-preview">
                    <div class="qr-placeholder">
                        <i class="fas fa-qrcode" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                        <p>输入内容后自动生成预览</p>
                    </div>
                </div>
                
                <div class="qr-actions" id="qr-actions" style="display: none;">
                    <button class="action-btn primary" onclick="downloadQR()">
                        <i class="fas fa-download"></i>
                        下载图片
                    </button>
                    <button class="action-btn" onclick="copyQRToClipboard()">
                        <i class="fas fa-copy"></i>
                        复制图片
                    </button>
                    <button class="action-btn" onclick="printQR()">
                        <i class="fas fa-print"></i>
                        打印
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentQRDataURL = null;
        
        // 更新预览
        function updatePreview() {
            const text = document.getElementById('qr-text').value.trim();
            if (text) {
                generateQR();
            } else {
                showPlaceholder();
            }
        }
        
        // 生成二维码
        async function generateQR() {
            const text = document.getElementById('qr-text').value.trim();
            if (!text) {
                showPlaceholder();
                return;
            }
            
            const size = parseInt(document.getElementById('qr-size').value);
            const errorLevel = document.getElementById('error-level').value;
            const fgColor = document.getElementById('fg-color').value;
            const bgColor = document.getElementById('bg-color').value;
            
            const generateBtn = document.getElementById('generate-btn');
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
            
            try {
                const canvas = document.createElement('canvas');
                await QRCode.toCanvas(canvas, text, {
                    width: size,
                    height: size,
                    errorCorrectionLevel: errorLevel,
                    color: {
                        dark: fgColor,
                        light: bgColor
                    },
                    margin: 2
                });
                
                currentQRDataURL = canvas.toDataURL('image/png');
                showQRCode(canvas);
                
            } catch (error) {
                console.error('生成二维码失败:', error);
                showError('生成失败，请检查输入内容');
            } finally {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成二维码';
            }
        }
        
        // 显示二维码
        function showQRCode(canvas) {
            const preview = document.getElementById('qr-preview');
            const actions = document.getElementById('qr-actions');
            
            preview.innerHTML = '';
            preview.appendChild(canvas);
            actions.style.display = 'flex';
        }
        
        // 显示占位符
        function showPlaceholder() {
            const preview = document.getElementById('qr-preview');
            const actions = document.getElementById('qr-actions');
            
            preview.innerHTML = `
                <div class="qr-placeholder">
                    <i class="fas fa-qrcode" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    <p>输入内容后自动生成预览</p>
                </div>
            `;
            actions.style.display = 'none';
            currentQRDataURL = null;
        }
        
        // 显示错误
        function showError(message) {
            const preview = document.getElementById('qr-preview');
            preview.innerHTML = `
                <div class="qr-placeholder">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px; display: block; color: #ef4444;"></i>
                    <p style="color: #ef4444;">${message}</p>
                </div>
            `;
        }
        
        // 下载二维码
        function downloadQR() {
            if (!currentQRDataURL) return;
            
            const link = document.createElement('a');
            link.download = `qrcode-${new Date().getTime()}.png`;
            link.href = currentQRDataURL;
            link.click();
        }
        
        // 复制到剪贴板
        async function copyQRToClipboard() {
            if (!currentQRDataURL) return;
            
            try {
                const response = await fetch(currentQRDataURL);
                const blob = await response.blob();
                await navigator.clipboard.write([
                    new ClipboardItem({ 'image/png': blob })
                ]);
                
                // 临时显示成功状态
                const btn = event.target.closest('.action-btn');
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                btn.style.background = '#10b981';
                btn.style.color = 'white';
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.style.background = '';
                    btn.style.color = '';
                }, 2000);
                
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请尝试下载图片');
            }
        }
        
        // 打印二维码
        function printQR() {
            if (!currentQRDataURL) return;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>打印二维码</title>
                        <style>
                            body { 
                                margin: 0; 
                                padding: 20px; 
                                display: flex; 
                                justify-content: center; 
                                align-items: center; 
                                min-height: 100vh;
                                font-family: Arial, sans-serif;
                            }
                            .print-container {
                                text-align: center;
                            }
                            img { 
                                max-width: 100%; 
                                height: auto; 
                            }
                            .print-info {
                                margin-top: 20px;
                                font-size: 12px;
                                color: #666;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="print-container">
                            <img src="${currentQRDataURL}" alt="二维码">
                            <div class="print-info">
                                <p>生成时间: ${new Date().toLocaleString()}</p>
                                <p>内容: ${document.getElementById('qr-text').value.substring(0, 50)}${document.getElementById('qr-text').value.length > 50 ? '...' : ''}</p>
                            </div>
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
        
        // 预设内容快速填充
        function fillPreset(type) {
            const textArea = document.getElementById('qr-text');
            
            switch(type) {
                case 'url':
                    textArea.value = 'https://www.example.com';
                    break;
                case 'wifi':
                    textArea.value = 'WIFI:T:WPA;S:WiFi名称;P:密码;H:false;;';
                    break;
                case 'email':
                    textArea.value = 'mailto:<EMAIL>?subject=主题&body=内容';
                    break;
                case 'phone':
                    textArea.value = 'tel:+86-138-0000-0000';
                    break;
                case 'sms':
                    textArea.value = 'sms:138-0000-0000:短信内容';
                    break;
            }
            
            updatePreview();
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加预设按钮
            const inputSection = document.querySelector('.input-section');
            const presetsHTML = `
                <div class="form-group">
                    <label class="form-label">快速模板</label>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button type="button" class="action-btn" onclick="fillPreset('url')" style="font-size: 12px; padding: 6px 12px;">
                            <i class="fas fa-link"></i> 网址
                        </button>
                        <button type="button" class="action-btn" onclick="fillPreset('wifi')" style="font-size: 12px; padding: 6px 12px;">
                            <i class="fas fa-wifi"></i> WiFi
                        </button>
                        <button type="button" class="action-btn" onclick="fillPreset('email')" style="font-size: 12px; padding: 6px 12px;">
                            <i class="fas fa-envelope"></i> 邮箱
                        </button>
                        <button type="button" class="action-btn" onclick="fillPreset('phone')" style="font-size: 12px; padding: 6px 12px;">
                            <i class="fas fa-phone"></i> 电话
                        </button>
                    </div>
                </div>
            `;
            
            inputSection.insertAdjacentHTML('beforeend', presetsHTML);
        });
    </script>
</body>
</html>
