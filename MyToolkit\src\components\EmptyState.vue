<template>
  <div class="empty-state">
    <div class="empty-content">
      <!-- 动画图标 -->
      <div class="empty-icon-wrapper">
        <ToolOutlined class="empty-icon" />
        <div class="icon-glow"></div>
      </div>

      <!-- 标题和描述 -->
      <h3 class="empty-title">选择一个工具开始使用</h3>
      <p class="empty-description">从左侧工具列表中选择您需要的工具，或者点击下方快速开始</p>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <Button type="primary" size="large" @click="openQRDecoder" class="action-btn">
          <QrcodeOutlined />
          二维码解码
        </Button>

        <Button size="large" @click="showAllTools" class="action-btn">
          <AppstoreOutlined />
          查看所有工具
        </Button>
      </div>

      <!-- 提示信息 -->
      <div class="tips">
        <div class="tip-item">
          <BulbOutlined class="tip-icon" />
          <span>支持拖拽文件到工具区域</span>
        </div>
        <div class="tip-item">
          <SettingOutlined class="tip-icon" />
          <span>使用 Ctrl+T 快速切换工具</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from 'ant-design-vue'
import {
  ToolOutlined,
  QrcodeOutlined,
  AppstoreOutlined,
  BulbOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { useAppStore } from '@/stores/app'
import { useRouter } from 'vue-router'

const appStore = useAppStore()
const router = useRouter()

const openQRDecoder = async () => {
  await appStore.openTool('qr-decoder')
  router.push('/qr-decoder')
}

const showAllTools = () => {
  // 滚动到侧边栏顶部
  const sidebar = document.querySelector('.tool-categories')
  if (sidebar) {
    sidebar.scrollTo({ top: 0, behavior: 'smooth' })
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.empty-content {
  text-align: center;
  max-width: 480px;
  padding: @spacing-xxl * 2;
  position: relative;
  z-index: 1;
}

.empty-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: @spacing-xxl;
}

.empty-icon {
  font-size: 80px;
  color: @primary-color;
  opacity: 0.8;
  animation: pulse 3s ease-in-out infinite;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
  to { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

.empty-title {
  font-size: @font-size-xl;
  font-weight: 600;
  color: @text-color;
  margin: 0 0 @spacing-sm 0;
  background: @gradient-primary;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-description {
  font-size: @font-size-sm;
  color: @text-color-secondary;
  margin: 0 0 @spacing-xxl * 1.5 0;
  line-height: 1.6;
}

.quick-actions {
  display: flex;
  justify-content: center;
  gap: @spacing-lg;
  margin-bottom: @spacing-xxl * 1.5;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: @border-radius-lg;
  height: 48px;
  padding: 0 @spacing-xxl;
  font-weight: 500;
  transition: all @transition-normal;
  box-shadow: @box-shadow-sm;

  &:hover {
    transform: translateY(-2px);
    box-shadow: @box-shadow-lg;
  }

  .anticon {
    margin-right: @spacing-sm;
  }
}

.tips {
  display: flex;
  flex-direction: column;
  gap: @spacing-md;
  padding-top: @spacing-xl;
  border-top: 1px solid @border-color-light;
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: @spacing-sm;
  font-size: @font-size-xs;
  color: @text-color-secondary;
  opacity: 0.8;

  .tip-icon {
    color: @primary-color;
    font-size: @font-size-sm;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .empty-content {
    padding: @spacing-xl;
    max-width: 320px;
  }

  .empty-icon {
    font-size: 60px;
  }

  .empty-title {
    font-size: @font-size-lg;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;

    .action-btn {
      width: 100%;
      max-width: 200px;
    }
  }

  .tips {
    .tip-item {
      font-size: 10px;
    }
  }
}
</style>
