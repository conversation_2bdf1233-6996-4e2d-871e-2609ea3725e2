@import './variables.less';

// Ant Design 主题定制
.ant-layout {
  background: @background-color;
}

.ant-menu {
  background: transparent;
  border: none;
  
  .ant-menu-item {
    border-radius: @border-radius-md;
    margin: @spacing-xs 0;
    
    &:hover {
      background: rgba(59, 130, 246, 0.1);
    }
    
    &.ant-menu-item-selected {
      background: rgba(59, 130, 246, 0.15);
      color: @primary-color;
    }
  }
}

.ant-btn {
  border-radius: @border-radius-md;
  font-weight: 500;
  transition: all @transition-normal;
  
  &.ant-btn-primary {
    background: @primary-color;
    border-color: @primary-color;
    
    &:hover {
      background: #2563eb;
      border-color: #2563eb;
      transform: translateY(-1px);
      box-shadow: @box-shadow-md;
    }
  }
  
  &.ant-btn-default {
    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }
}

.ant-input {
  border-radius: @border-radius-md;
  transition: all @transition-normal;
  
  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &:hover {
    border-color: @primary-color;
  }
}

.ant-input-affix-wrapper {
  border-radius: @border-radius-md;
  transition: all @transition-normal;
  
  &:focus-within {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
  
  &:hover {
    border-color: @primary-color;
  }
}

.ant-card {
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-sm;
  border: 1px solid @border-color;
  
  .ant-card-head {
    border-bottom: 1px solid @border-color;
  }
}

.ant-tabs {
  .ant-tabs-nav {
    &::before {
      border-bottom: 1px solid @border-color;
    }
  }
  
  .ant-tabs-tab {
    border-radius: @border-radius-md @border-radius-md 0 0;
    
    &:hover {
      color: @primary-color;
    }
    
    &.ant-tabs-tab-active {
      .ant-tabs-tab-btn {
        color: @primary-color;
      }
    }
  }
}

.ant-modal {
  .ant-modal-content {
    border-radius: @border-radius-lg;
    box-shadow: @box-shadow-xl;
  }
  
  .ant-modal-header {
    border-bottom: 1px solid @border-color;
    border-radius: @border-radius-lg @border-radius-lg 0 0;
  }
  
  .ant-modal-footer {
    border-top: 1px solid @border-color;
    border-radius: 0 0 @border-radius-lg @border-radius-lg;
  }
}

.ant-notification {
  .ant-notification-notice {
    border-radius: @border-radius-lg;
    box-shadow: @box-shadow-lg;
  }
}

.ant-message {
  .ant-message-notice {
    .ant-message-notice-content {
      border-radius: @border-radius-lg;
      box-shadow: @box-shadow-md;
    }
  }
}

.ant-tooltip {
  .ant-tooltip-inner {
    border-radius: @border-radius-md;
    background: rgba(0, 0, 0, 0.85);
  }
}

.ant-popover {
  .ant-popover-content {
    .ant-popover-inner {
      border-radius: @border-radius-lg;
      box-shadow: @box-shadow-lg;
    }
  }
}

.ant-dropdown {
  .ant-dropdown-menu {
    border-radius: @border-radius-lg;
    box-shadow: @box-shadow-lg;
    border: 1px solid @border-color;
    
    .ant-dropdown-menu-item {
      border-radius: @border-radius-sm;
      margin: @spacing-xs;
      
      &:hover {
        background: rgba(59, 130, 246, 0.1);
      }
    }
  }
}

.ant-select {
  .ant-select-selector {
    border-radius: @border-radius-md;
    
    &:hover {
      border-color: @primary-color;
    }
  }
  
  &.ant-select-focused {
    .ant-select-selector {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
}

.ant-upload {
  &.ant-upload-drag {
    border-radius: @border-radius-lg;
    border: 2px dashed @border-color;
    transition: all @transition-normal;
    
    &:hover {
      border-color: @primary-color;
    }
    
    &.ant-upload-drag-hover {
      border-color: @primary-color;
      background: rgba(59, 130, 246, 0.05);
    }
  }
}

// 自定义滚动条
.ant-table-body::-webkit-scrollbar,
.ant-select-dropdown::-webkit-scrollbar,
.ant-cascader-menu::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track,
.ant-select-dropdown::-webkit-scrollbar-track,
.ant-cascader-menu::-webkit-scrollbar-track {
  background: transparent;
}

.ant-table-body::-webkit-scrollbar-thumb,
.ant-select-dropdown::-webkit-scrollbar-thumb,
.ant-cascader-menu::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  
  &:hover {
    background: #94a3b8;
  }
}
