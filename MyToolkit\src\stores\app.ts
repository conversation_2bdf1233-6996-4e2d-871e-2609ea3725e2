import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getAppVersion, getAppName, saveToolState, loadToolState, safeTauriInvoke } from '@/utils/tauri'
import type { Tool, AppConfig } from '@/types'

export const useAppStore = defineStore('app', () => {
  // 状态
  const appVersion = ref('')
  const appName = ref('')
  const isLoading = ref(false)
  const config = ref<AppConfig>({
    theme: 'light',
    language: 'zh-CN',
    autoSave: true,
    showWelcome: true,
  })
  
  // 工具相关状态
  const availableTools = ref<Tool[]>([
    {
      id: 'qr-decoder',
      name: '二维码解码',
      description: '解析二维码图片内容',
      icon: 'qrcode',
      category: 'image',
      route: '/qr-decoder',
      enabled: true,
    },
    {
      id: 'qr-generator',
      name: '二维码生成',
      description: '生成二维码图片',
      icon: 'plus-square',
      category: 'image',
      route: '/qr-generator',
      enabled: false, // 第二版本功能
    },
    {
      id: 'json-formatter',
      name: 'JSON格式化',
      description: 'JSON美化和压缩',
      icon: 'code',
      category: 'text',
      route: '/json-formatter',
      enabled: false, // 第二版本功能
    },
  ])
  
  const openTabs = ref<string[]>([])
  const activeTab = ref<string>('')
  
  // 计算属性
  const enabledTools = computed(() => 
    availableTools.value.filter(tool => tool.enabled)
  )
  
  const toolsByCategory = computed(() => {
    const categories: Record<string, Tool[]> = {}
    enabledTools.value.forEach(tool => {
      if (!categories[tool.category]) {
        categories[tool.category] = []
      }
      categories[tool.category].push(tool)
    })
    return categories
  })
  
  // 动作
  const initializeApp = async () => {
    try {
      isLoading.value = true

      // 获取应用信息
      appVersion.value = await getAppVersion()
      appName.value = await getAppName()

      // 加载配置
      await loadConfig()

      // 加载Tab状态
      await loadTabState()

      console.log(`${appName.value} v${appVersion.value} 初始化完成`)
    } catch (error) {
      console.error('应用初始化失败:', error)
    } finally {
      isLoading.value = false
    }
  }
  
  const loadConfig = async () => {
    try {
      const savedConfig = await loadToolState('app-config')
      if (savedConfig) {
        config.value = { ...config.value, ...savedConfig }
      }
    } catch (error) {
      console.warn('加载配置失败:', error)
    }
  }

  const saveConfig = async () => {
    try {
      await saveToolState('app-config', config.value)
    } catch (error) {
      console.error('保存配置失败:', error)
    }
  }
  
  const openTool = async (toolId: string) => {
    if (!openTabs.value.includes(toolId)) {
      openTabs.value.push(toolId)
    }
    activeTab.value = toolId

    // 持久化Tab状态
    await saveTabState()
  }

  const closeTool = async (toolId: string) => {
    const index = openTabs.value.indexOf(toolId)
    if (index > -1) {
      openTabs.value.splice(index, 1)

      // 如果关闭的是当前活动标签，切换到其他标签
      if (activeTab.value === toolId) {
        if (openTabs.value.length > 0) {
          activeTab.value = openTabs.value[Math.max(0, index - 1)]
        } else {
          activeTab.value = ''
        }
      }

      // 持久化Tab状态
      await saveTabState()
    }
  }

  const switchTab = async (toolId: string) => {
    if (openTabs.value.includes(toolId)) {
      activeTab.value = toolId

      // 持久化Tab状态
      await saveTabState()
    }
  }

  const saveTabState = async () => {
    try {
      const tabState = {
        openTabs: openTabs.value,
        activeTab: activeTab.value,
        timestamp: Date.now()
      }
      await saveToolState('tab-state', tabState)
    } catch (error) {
      console.error('保存Tab状态失败:', error)
    }
  }

  const loadTabState = async () => {
    try {
      const tabState = await loadToolState('tab-state')
      if (tabState) {
        // 只恢复有效的工具Tab
        const validTabs = tabState.openTabs.filter((toolId: string) =>
          availableTools.value.some(tool => tool.id === toolId && tool.enabled)
        )

        if (validTabs.length > 0) {
          openTabs.value = validTabs
          // 确保活动Tab是有效的
          if (validTabs.includes(tabState.activeTab)) {
            activeTab.value = tabState.activeTab
          } else {
            activeTab.value = validTabs[0]
          }
        }
      }
    } catch (error) {
      console.warn('加载Tab状态失败:', error)
    }
  }

  const clearTabState = async () => {
    openTabs.value = []
    activeTab.value = ''
    await saveTabState()
  }
  
  const getToolById = (toolId: string) => {
    return availableTools.value.find(tool => tool.id === toolId)
  }
  
  return {
    // 状态
    appVersion,
    appName,
    isLoading,
    config,
    availableTools,
    openTabs,
    activeTab,

    // 计算属性
    enabledTools,
    toolsByCategory,

    // 动作
    initializeApp,
    loadConfig,
    saveConfig,
    openTool,
    closeTool,
    switchTab,
    saveTabState,
    loadTabState,
    clearTabState,
    getToolById,
  }
})
