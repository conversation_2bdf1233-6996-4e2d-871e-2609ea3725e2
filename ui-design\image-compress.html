<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片压缩工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8fafc;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .upload-area.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .file-list {
            flex: 1;
            overflow-y: auto;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 8px;
            background: #f8fafc;
        }
        
        .file-preview {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            margin-right: 12px;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .file-size {
            font-size: 12px;
            color: #64748b;
        }
        
        .compression-stats {
            text-align: center;
            margin: 8px 0;
        }
        
        .size-reduction {
            color: #10b981;
            font-weight: 500;
        }
        
        .file-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .action-btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .action-btn.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .settings-panel {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .form-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s;
        }
        
        .batch-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .batch-btn {
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title" style="display: flex; align-items: center; gap: 12px;">
                <div class="tool-icon">
                    <i class="fas fa-compress-alt"></i>
                </div>
                <div>
                    <h1 style="margin: 0; font-size: 24px; font-weight: 600;">图片压缩工具</h1>
                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">批量压缩图片，减小文件大小</p>
                </div>
            </div>
        </div>
        
        <!-- 设置面板 -->
        <div class="settings-panel">
            <div class="settings-grid">
                <div class="form-group">
                    <label class="form-label">压缩质量</label>
                    <input type="range" class="form-input" id="quality" min="10" max="100" value="80" 
                           oninput="document.getElementById('quality-value').textContent = this.value + '%'">
                    <div style="text-align: center; font-size: 12px; color: #64748b; margin-top: 4px;">
                        <span id="quality-value">80%</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">输出格式</label>
                    <select class="form-input" id="output-format">
                        <option value="original">保持原格式</option>
                        <option value="jpeg">JPEG</option>
                        <option value="png">PNG</option>
                        <option value="webp">WebP</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">最大宽度 (px)</label>
                    <input type="number" class="form-input" id="max-width" placeholder="不限制" min="100" max="4096">
                </div>
                
                <div class="form-group">
                    <label class="form-label">最大高度 (px)</label>
                    <input type="number" class="form-input" id="max-height" placeholder="不限制" min="100" max="4096">
                </div>
            </div>
        </div>
        
        <!-- 上传区域 -->
        <div class="upload-area" id="upload-area" onclick="selectFiles()">
            <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #94a3b8; margin-bottom: 16px;"></i>
            <h3 style="margin: 0 0 8px 0; font-size: 18px; color: #1e293b;">拖拽图片到此处</h3>
            <p style="margin: 0; color: #64748b;">或点击选择多个图片文件</p>
            <p style="margin: 8px 0 0 0; font-size: 12px; color: #94a3b8;">支持 JPG、PNG、GIF、WebP 格式</p>
        </div>
        
        <!-- 文件列表 -->
        <div class="file-list" id="file-list" style="display: none;">
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 16px; color: #1e293b;">文件列表</h3>
                <div style="font-size: 12px; color: #64748b;">
                    <span id="file-count">0</span> 个文件
                </div>
            </div>
            <div id="file-items"></div>
        </div>
        
        <!-- 批量操作 -->
        <div class="batch-actions" id="batch-actions" style="display: none;">
            <button class="batch-btn btn-primary" onclick="compressAll()">
                <i class="fas fa-compress-alt"></i>
                压缩所有图片
            </button>
            <button class="batch-btn btn-success" onclick="downloadAll()" id="download-all-btn" style="display: none;">
                <i class="fas fa-download"></i>
                下载所有压缩图片
            </button>
            <button class="batch-btn btn-secondary" onclick="clearAll()">
                <i class="fas fa-trash"></i>
                清空列表
            </button>
        </div>
    </div>
    
    <input type="file" id="file-input" multiple accept="image/*" style="display: none;" onchange="handleFiles(this.files)">

    <script>
        let fileList = [];
        let compressedFiles = [];
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupDragAndDrop();
        });
        
        // 设置拖拽功能
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('upload-area');
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        }
        
        // 选择文件
        function selectFiles() {
            document.getElementById('file-input').click();
        }
        
        // 处理文件
        function handleFiles(files) {
            const validFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
            
            if (validFiles.length === 0) {
                alert('请选择有效的图片文件');
                return;
            }
            
            validFiles.forEach(file => {
                const fileId = Date.now() + Math.random();
                fileList.push({
                    id: fileId,
                    file: file,
                    compressed: null,
                    status: 'pending'
                });
            });
            
            updateFileList();
            showFileList();
        }
        
        // 显示文件列表
        function showFileList() {
            document.getElementById('file-list').style.display = 'block';
            document.getElementById('batch-actions').style.display = 'flex';
            document.getElementById('upload-area').style.display = 'none';
        }
        
        // 更新文件列表
        function updateFileList() {
            const fileItems = document.getElementById('file-items');
            const fileCount = document.getElementById('file-count');
            
            fileCount.textContent = fileList.length;
            fileItems.innerHTML = '';
            
            fileList.forEach(item => {
                const fileItem = createFileItem(item);
                fileItems.appendChild(fileItem);
            });
        }
        
        // 创建文件项
        function createFileItem(item) {
            const div = document.createElement('div');
            div.className = 'file-item';
            div.id = `file-${item.id}`;
            
            // 创建预览图
            const img = document.createElement('img');
            img.className = 'file-preview';
            img.src = URL.createObjectURL(item.file);
            
            // 文件信息
            const originalSize = formatFileSize(item.file.size);
            const compressedSize = item.compressed ? formatFileSize(item.compressed.size) : '-';
            const reduction = item.compressed ? 
                Math.round((1 - item.compressed.size / item.file.size) * 100) : 0;
            
            div.innerHTML = `
                <img src="${img.src}" class="file-preview" alt="预览">
                <div class="file-info">
                    <div class="file-name">${item.file.name}</div>
                    <div class="file-size">
                        原始: ${originalSize} 
                        ${item.compressed ? `→ 压缩后: ${compressedSize}` : ''}
                    </div>
                    ${item.compressed ? `
                        <div class="compression-stats">
                            <span class="size-reduction">减少 ${reduction}%</span>
                        </div>
                    ` : ''}
                    ${item.status === 'compressing' ? `
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 50%;"></div>
                        </div>
                    ` : ''}
                </div>
                <div class="file-actions">
                    ${item.status === 'pending' ? `
                        <button class="action-btn primary" onclick="compressFile('${item.id}')">
                            <i class="fas fa-compress-alt"></i> 压缩
                        </button>
                    ` : ''}
                    ${item.status === 'completed' ? `
                        <button class="action-btn success" onclick="downloadFile('${item.id}')">
                            <i class="fas fa-download"></i> 下载
                        </button>
                    ` : ''}
                    <button class="action-btn" onclick="removeFile('${item.id}')">
                        <i class="fas fa-times"></i> 移除
                    </button>
                </div>
            `;
            
            return div;
        }
        
        // 压缩单个文件
        async function compressFile(fileId) {
            const item = fileList.find(f => f.id == fileId);
            if (!item) return;
            
            item.status = 'compressing';
            updateFileList();
            
            try {
                const compressed = await compressImage(item.file);
                item.compressed = compressed;
                item.status = 'completed';
                
                compressedFiles.push({
                    id: fileId,
                    name: item.file.name,
                    blob: compressed
                });
                
                updateFileList();
                updateDownloadButton();
                
            } catch (error) {
                console.error('压缩失败:', error);
                item.status = 'error';
                updateFileList();
            }
        }
        
        // 压缩图片
        function compressImage(file) {
            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    const quality = document.getElementById('quality').value / 100;
                    const maxWidth = parseInt(document.getElementById('max-width').value) || img.width;
                    const maxHeight = parseInt(document.getElementById('max-height').value) || img.height;
                    const outputFormat = document.getElementById('output-format').value;
                    
                    // 计算新尺寸
                    let { width, height } = calculateNewSize(img.width, img.height, maxWidth, maxHeight);
                    
                    canvas.width = width;
                    canvas.height = height;
                    
                    // 绘制图片
                    ctx.drawImage(img, 0, 0, width, height);
                    
                    // 确定输出格式
                    let mimeType = file.type;
                    if (outputFormat !== 'original') {
                        mimeType = `image/${outputFormat}`;
                    }
                    
                    // 转换为blob
                    canvas.toBlob(resolve, mimeType, quality);
                };
                
                img.onerror = reject;
                img.src = URL.createObjectURL(file);
            });
        }
        
        // 计算新尺寸
        function calculateNewSize(originalWidth, originalHeight, maxWidth, maxHeight) {
            let width = originalWidth;
            let height = originalHeight;
            
            // 按比例缩放
            if (width > maxWidth) {
                height = (height * maxWidth) / width;
                width = maxWidth;
            }
            
            if (height > maxHeight) {
                width = (width * maxHeight) / height;
                height = maxHeight;
            }
            
            return { width: Math.round(width), height: Math.round(height) };
        }
        
        // 压缩所有文件
        async function compressAll() {
            const pendingFiles = fileList.filter(f => f.status === 'pending');
            
            for (const item of pendingFiles) {
                await compressFile(item.id);
            }
        }
        
        // 下载单个文件
        function downloadFile(fileId) {
            const compressed = compressedFiles.find(f => f.id == fileId);
            if (!compressed) return;
            
            const url = URL.createObjectURL(compressed.blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `compressed_${compressed.name}`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 下载所有压缩文件
        function downloadAll() {
            compressedFiles.forEach(file => {
                downloadFile(file.id);
            });
        }
        
        // 移除文件
        function removeFile(fileId) {
            fileList = fileList.filter(f => f.id != fileId);
            compressedFiles = compressedFiles.filter(f => f.id != fileId);
            
            if (fileList.length === 0) {
                clearAll();
            } else {
                updateFileList();
                updateDownloadButton();
            }
        }
        
        // 清空所有
        function clearAll() {
            fileList = [];
            compressedFiles = [];
            document.getElementById('file-list').style.display = 'none';
            document.getElementById('batch-actions').style.display = 'none';
            document.getElementById('upload-area').style.display = 'flex';
            document.getElementById('file-input').value = '';
        }
        
        // 更新下载按钮
        function updateDownloadButton() {
            const downloadBtn = document.getElementById('download-all-btn');
            downloadBtn.style.display = compressedFiles.length > 0 ? 'flex' : 'none';
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
