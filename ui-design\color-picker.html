<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色选择器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            overflow: hidden;
        }
        
        .color-picker-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            display: flex;
            flex-direction: column;
        }
        
        .color-display {
            width: 100%;
            height: 200px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .color-info {
            position: absolute;
            bottom: 16px;
            left: 16px;
            right: 16px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            font-weight: 500;
        }
        
        .color-input-group {
            margin-bottom: 16px;
        }
        
        .color-input-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .color-input {
            width: 100%;
            height: 50px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .color-input:hover {
            border-color: #3b82f6;
        }
        
        .text-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        .text-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .color-formats {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            overflow-y: auto;
        }
        
        .format-group {
            margin-bottom: 20px;
        }
        
        .format-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .format-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        
        .format-label {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }
        
        .format-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #1e293b;
            background: white;
            padding: 6px 10px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            min-width: 120px;
            text-align: center;
        }
        
        .copy-btn {
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            margin-left: 8px;
        }
        
        .copy-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .palette-section {
            margin-top: 20px;
        }
        
        .palette-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .palette-color {
            width: 100%;
            height: 40px;
            border-radius: 6px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .palette-color:hover {
            border-color: #3b82f6;
            transform: scale(1.1);
        }
        
        .palette-color.selected {
            border-color: #1e293b;
            box-shadow: 0 0 0 2px #3b82f6;
        }
        
        .sliders-section {
            margin-top: 20px;
        }
        
        .slider-group {
            margin-bottom: 16px;
        }
        
        .slider-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            font-size: 14px;
            color: #374151;
        }
        
        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            outline: none;
            cursor: pointer;
        }
        
        .slider::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title" style="display: flex; align-items: center; gap: 12px;">
                <div class="tool-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div>
                    <h1 style="margin: 0; font-size: 24px; font-weight: 600;">颜色选择器</h1>
                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">RGB、HEX、HSL颜色转换</p>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 颜色选择区域 -->
            <div class="color-picker-section">
                <!-- 颜色显示 -->
                <div class="color-display" id="color-display">
                    <div class="color-info" id="color-info">
                        #3B82F6 - RGB(59, 130, 246)
                    </div>
                </div>
                
                <!-- 颜色输入 -->
                <div class="color-input-group">
                    <label class="color-input-label">选择颜色</label>
                    <input type="color" class="color-input" id="color-picker" value="#3b82f6" onchange="updateColor(this.value)">
                </div>
                
                <div class="color-input-group">
                    <label class="color-input-label">输入颜色值</label>
                    <input type="text" class="text-input" id="color-text" placeholder="输入 HEX、RGB 或 HSL 值" onchange="parseColorInput(this.value)">
                </div>
                
                <!-- RGB滑块 -->
                <div class="sliders-section">
                    <h3 style="margin: 0 0 16px 0; font-size: 16px; color: #1e293b;">RGB 调节</h3>
                    
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>红色 (R)</span>
                            <span id="r-value">59</span>
                        </div>
                        <input type="range" class="slider" id="r-slider" min="0" max="255" value="59" oninput="updateFromSliders()">
                    </div>
                    
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>绿色 (G)</span>
                            <span id="g-value">130</span>
                        </div>
                        <input type="range" class="slider" id="g-slider" min="0" max="255" value="130" oninput="updateFromSliders()">
                    </div>
                    
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>蓝色 (B)</span>
                            <span id="b-value">246</span>
                        </div>
                        <input type="range" class="slider" id="b-slider" min="0" max="255" value="246" oninput="updateFromSliders()">
                    </div>
                </div>
                
                <!-- 预设调色板 -->
                <div class="palette-section">
                    <h3 style="margin: 0 0 12px 0; font-size: 16px; color: #1e293b;">常用颜色</h3>
                    <div class="palette-grid" id="palette-grid">
                        <!-- 动态生成调色板 -->
                    </div>
                </div>
            </div>
            
            <!-- 颜色格式区域 -->
            <div class="color-formats">
                <div class="format-group">
                    <div class="format-title">
                        <i class="fas fa-hashtag"></i>
                        十六进制 (HEX)
                    </div>
                    <div class="format-item">
                        <span class="format-label">HEX</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="hex-value">#3B82F6</span>
                            <button class="copy-btn" onclick="copyValue('hex-value')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="format-item">
                        <span class="format-label">HEX (无#)</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="hex-no-hash">3B82F6</span>
                            <button class="copy-btn" onclick="copyValue('hex-no-hash')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="format-group">
                    <div class="format-title">
                        <i class="fas fa-circle"></i>
                        RGB 格式
                    </div>
                    <div class="format-item">
                        <span class="format-label">RGB</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="rgb-value">rgb(59, 130, 246)</span>
                            <button class="copy-btn" onclick="copyValue('rgb-value')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="format-item">
                        <span class="format-label">RGB 数值</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="rgb-numbers">59, 130, 246</span>
                            <button class="copy-btn" onclick="copyValue('rgb-numbers')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="format-group">
                    <div class="format-title">
                        <i class="fas fa-adjust"></i>
                        HSL 格式
                    </div>
                    <div class="format-item">
                        <span class="format-label">HSL</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="hsl-value">hsl(217, 91%, 60%)</span>
                            <button class="copy-btn" onclick="copyValue('hsl-value')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="format-item">
                        <span class="format-label">HSL 数值</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="hsl-numbers">217, 91%, 60%</span>
                            <button class="copy-btn" onclick="copyValue('hsl-numbers')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="format-group">
                    <div class="format-title">
                        <i class="fas fa-code"></i>
                        CSS 格式
                    </div>
                    <div class="format-item">
                        <span class="format-label">CSS 变量</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="css-var">--color: #3B82F6;</span>
                            <button class="copy-btn" onclick="copyValue('css-var')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="format-item">
                        <span class="format-label">RGBA (透明)</span>
                        <div style="display: flex; align-items: center;">
                            <span class="format-value" id="rgba-value">rgba(59, 130, 246, 0.8)</span>
                            <button class="copy-btn" onclick="copyValue('rgba-value')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentColor = { r: 59, g: 130, b: 246 };
        
        // 预设颜色调色板
        const presetColors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
            '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
            '#F1948A', '#85C1E9', '#F4D03F', '#A569BD', '#5DADE2', '#58D68D',
            '#EC7063', '#3498DB', '#E74C3C', '#9B59B6', '#1ABC9C', '#F39C12',
            '#2ECC71', '#E67E22', '#34495E', '#95A5A6', '#16A085', '#27AE60',
            '#2980B9', '#8E44AD', '#F1C40F', '#E67E22', '#E74C3C', '#ECF0F1'
        ];
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generatePalette();
            updateAllFormats();
        });
        
        // 生成调色板
        function generatePalette() {
            const paletteGrid = document.getElementById('palette-grid');
            paletteGrid.innerHTML = '';
            
            presetColors.forEach(color => {
                const colorDiv = document.createElement('div');
                colorDiv.className = 'palette-color';
                colorDiv.style.backgroundColor = color;
                colorDiv.title = color;
                colorDiv.onclick = () => updateColor(color);
                paletteGrid.appendChild(colorDiv);
            });
        }
        
        // 更新颜色
        function updateColor(colorValue) {
            const rgb = hexToRgb(colorValue);
            if (rgb) {
                currentColor = rgb;
                updateDisplay();
                updateSliders();
                updateAllFormats();
                
                // 更新颜色选择器
                document.getElementById('color-picker').value = rgbToHex(rgb.r, rgb.g, rgb.b);
            }
        }
        
        // 从滑块更新
        function updateFromSliders() {
            const r = parseInt(document.getElementById('r-slider').value);
            const g = parseInt(document.getElementById('g-slider').value);
            const b = parseInt(document.getElementById('b-slider').value);
            
            currentColor = { r, g, b };
            
            // 更新滑块显示值
            document.getElementById('r-value').textContent = r;
            document.getElementById('g-value').textContent = g;
            document.getElementById('b-value').textContent = b;
            
            updateDisplay();
            updateAllFormats();
            
            // 更新颜色选择器
            document.getElementById('color-picker').value = rgbToHex(r, g, b);
        }
        
        // 更新滑块
        function updateSliders() {
            document.getElementById('r-slider').value = currentColor.r;
            document.getElementById('g-slider').value = currentColor.g;
            document.getElementById('b-slider').value = currentColor.b;
            
            document.getElementById('r-value').textContent = currentColor.r;
            document.getElementById('g-value').textContent = currentColor.g;
            document.getElementById('b-value').textContent = currentColor.b;
        }
        
        // 更新显示
        function updateDisplay() {
            const colorDisplay = document.getElementById('color-display');
            const colorInfo = document.getElementById('color-info');
            const hex = rgbToHex(currentColor.r, currentColor.g, currentColor.b);
            
            colorDisplay.style.backgroundColor = `rgb(${currentColor.r}, ${currentColor.g}, ${currentColor.b})`;
            colorInfo.textContent = `${hex} - RGB(${currentColor.r}, ${currentColor.g}, ${currentColor.b})`;
            
            // 根据颜色亮度调整文字颜色
            const brightness = (currentColor.r * 299 + currentColor.g * 587 + currentColor.b * 114) / 1000;
            colorInfo.style.color = brightness > 128 ? '#000' : '#fff';
        }
        
        // 更新所有格式
        function updateAllFormats() {
            const hex = rgbToHex(currentColor.r, currentColor.g, currentColor.b);
            const hsl = rgbToHsl(currentColor.r, currentColor.g, currentColor.b);
            
            // HEX 格式
            document.getElementById('hex-value').textContent = hex;
            document.getElementById('hex-no-hash').textContent = hex.substring(1);
            
            // RGB 格式
            document.getElementById('rgb-value').textContent = `rgb(${currentColor.r}, ${currentColor.g}, ${currentColor.b})`;
            document.getElementById('rgb-numbers').textContent = `${currentColor.r}, ${currentColor.g}, ${currentColor.b}`;
            
            // HSL 格式
            document.getElementById('hsl-value').textContent = `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
            document.getElementById('hsl-numbers').textContent = `${hsl.h}, ${hsl.s}%, ${hsl.l}%`;
            
            // CSS 格式
            document.getElementById('css-var').textContent = `--color: ${hex};`;
            document.getElementById('rgba-value').textContent = `rgba(${currentColor.r}, ${currentColor.g}, ${currentColor.b}, 0.8)`;
        }
        
        // 解析颜色输入
        function parseColorInput(value) {
            value = value.trim();
            
            // HEX 格式
            if (value.match(/^#?[0-9A-Fa-f]{6}$/)) {
                if (!value.startsWith('#')) value = '#' + value;
                updateColor(value);
                return;
            }
            
            // RGB 格式
            const rgbMatch = value.match(/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
            if (rgbMatch) {
                const r = parseInt(rgbMatch[1]);
                const g = parseInt(rgbMatch[2]);
                const b = parseInt(rgbMatch[3]);
                if (r <= 255 && g <= 255 && b <= 255) {
                    currentColor = { r, g, b };
                    updateDisplay();
                    updateSliders();
                    updateAllFormats();
                    document.getElementById('color-picker').value = rgbToHex(r, g, b);
                    return;
                }
            }
            
            // HSL 格式
            const hslMatch = value.match(/hsl\s*\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/);
            if (hslMatch) {
                const h = parseInt(hslMatch[1]);
                const s = parseInt(hslMatch[2]);
                const l = parseInt(hslMatch[3]);
                const rgb = hslToRgb(h, s, l);
                currentColor = rgb;
                updateDisplay();
                updateSliders();
                updateAllFormats();
                document.getElementById('color-picker').value = rgbToHex(rgb.r, rgb.g, rgb.b);
                return;
            }
            
            alert('无法识别的颜色格式');
        }
        
        // 复制值
        async function copyValue(elementId) {
            const element = document.getElementById(elementId);
            const value = element.textContent;
            
            try {
                await navigator.clipboard.writeText(value);
                
                const button = event.target.closest('.copy-btn');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.style.background = '#10b981';
                button.style.color = 'white';
                button.style.borderColor = '#10b981';
                
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.style.background = '';
                    button.style.color = '';
                    button.style.borderColor = '';
                }, 1500);
                
            } catch (error) {
                alert('复制失败');
            }
        }
        
        // 颜色转换函数
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
        
        function rgbToHex(r, g, b) {
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
        }
        
        function rgbToHsl(r, g, b) {
            r /= 255;
            g /= 255;
            b /= 255;
            
            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;
            
            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                
                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }
            
            return {
                h: Math.round(h * 360),
                s: Math.round(s * 100),
                l: Math.round(l * 100)
            };
        }
        
        function hslToRgb(h, s, l) {
            h /= 360;
            s /= 100;
            l /= 100;
            
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };
            
            let r, g, b;
            
            if (s === 0) {
                r = g = b = l;
            } else {
                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
            }
            
            return {
                r: Math.round(r * 255),
                g: Math.round(g * 255),
                b: Math.round(b * 255)
            };
        }
    </script>
</body>
</html>
