/**
 * 二维码解码工具函数
 */

import jsQR from 'jsqr'

// 二维码解码结果接口
export interface QRDecodeResult {
  success: boolean
  data?: string
  error?: string
  location?: {
    topLeftCorner: { x: number; y: number }
    topRightCorner: { x: number; y: number }
    bottomLeftCorner: { x: number; y: number }
    bottomRightCorner: { x: number; y: number }
  }
}

// 支持的图片格式
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp'
]

// 最大文件大小 (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024

/**
 * 验证文件是否为支持的图片格式
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  if (!file) {
    return { valid: false, error: '请选择文件' }
  }

  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    return { 
      valid: false, 
      error: `不支持的文件格式。支持的格式：${SUPPORTED_IMAGE_TYPES.join(', ')}` 
    }
  }

  if (file.size > MAX_FILE_SIZE) {
    return { 
      valid: false, 
      error: `文件大小超过限制。最大支持 ${Math.round(MAX_FILE_SIZE / 1024 / 1024)}MB` 
    }
  }

  return { valid: true }
}

/**
 * 将文件转换为Base64数据URL
 */
export function fileToDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string)
      } else {
        reject(new Error('文件读取失败'))
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsDataURL(file)
  })
}

/**
 * 将图片URL转换为ImageData
 */
export function imageToImageData(imageSrc: string): Promise<ImageData> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          reject(new Error('无法创建Canvas上下文'))
          return
        }

        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        resolve(imageData)
      } catch (error) {
        reject(new Error('图片处理失败'))
      }
    }
    img.onerror = () => reject(new Error('图片加载失败'))
    img.src = imageSrc
  })
}

/**
 * 图片预处理 - 提高二维码识别率
 */
export function preprocessImage(imageData: ImageData): ImageData {
  const data = new Uint8ClampedArray(imageData.data)
  
  // 转换为灰度图像并增强对比度
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]
    
    // 计算灰度值
    const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b)
    
    // 增强对比度 - 简单的阈值处理
    const enhanced = gray > 128 ? 255 : 0
    
    data[i] = enhanced     // R
    data[i + 1] = enhanced // G
    data[i + 2] = enhanced // B
    // Alpha通道保持不变
  }
  
  return new ImageData(data, imageData.width, imageData.height)
}

/**
 * 解码二维码
 */
export async function decodeQRCode(imageSrc: string): Promise<QRDecodeResult> {
  try {
    // 将图片转换为ImageData
    const imageData = await imageToImageData(imageSrc)
    
    // 首先尝试直接解码
    let code = jsQR(imageData.data, imageData.width, imageData.height, {
      inversionAttempts: 'dontInvert'
    })
    
    // 如果直接解码失败，尝试预处理后解码
    if (!code) {
      const preprocessedData = preprocessImage(imageData)
      code = jsQR(preprocessedData.data, preprocessedData.width, preprocessedData.height, {
        inversionAttempts: 'attemptBoth'
      })
    }
    
    if (code) {
      return {
        success: true,
        data: code.data,
        location: code.location
      }
    } else {
      return {
        success: false,
        error: '未检测到二维码内容'
      }
    }
  } catch (error) {
    console.error('二维码解码失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '解码过程中发生错误'
    }
  }
}

/**
 * 从文件解码二维码
 */
export async function decodeQRCodeFromFile(file: File): Promise<QRDecodeResult> {
  // 验证文件
  const validation = validateImageFile(file)
  if (!validation.valid) {
    return {
      success: false,
      error: validation.error
    }
  }

  try {
    // 转换为数据URL
    const dataURL = await fileToDataURL(file)
    
    // 解码二维码
    return await decodeQRCode(dataURL)
  } catch (error) {
    console.error('从文件解码二维码失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '文件处理失败'
    }
  }
}

/**
 * 从剪贴板解码二维码
 */
export async function decodeQRCodeFromClipboard(): Promise<QRDecodeResult> {
  try {
    // 尝试读取剪贴板中的图片
    const clipboardItems = await navigator.clipboard.read()
    
    for (const clipboardItem of clipboardItems) {
      for (const type of clipboardItem.types) {
        if (type.startsWith('image/')) {
          const blob = await clipboardItem.getType(type)
          const file = new File([blob], 'clipboard-image', { type })
          return await decodeQRCodeFromFile(file)
        }
      }
    }
    
    return {
      success: false,
      error: '剪贴板中没有找到图片'
    }
  } catch (error) {
    console.error('从剪贴板解码二维码失败:', error)
    return {
      success: false,
      error: '无法访问剪贴板或剪贴板中没有图片'
    }
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 生成唯一的文件名
 */
export function generateFileName(prefix: string = 'qr-decode-result', extension: string = 'txt'): string {
  const timestamp = new Date().getTime()
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}-${timestamp}-${random}.${extension}`
}
