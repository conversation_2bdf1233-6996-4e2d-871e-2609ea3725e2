<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哈希生成工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .input-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .form-textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            resize: vertical;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .hash-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .hash-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .hash-option:hover {
            border-color: #3b82f6;
        }
        
        .hash-option.selected {
            background: #eff6ff;
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .hash-checkbox {
            width: 16px;
            height: 16px;
        }
        
        .generate-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            width: 100%;
        }
        
        .generate-btn:hover {
            background: #2563eb;
        }
        
        .results-section {
            flex: 1;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            overflow-y: auto;
        }
        
        .result-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        
        .result-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .result-title {
            font-weight: 500;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .result-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .result-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            color: #374151;
            word-break: break-all;
            line-height: 1.5;
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #94a3b8;
            text-align: center;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title" style="display: flex; align-items: center; gap: 12px;">
                <div class="tool-icon">
                    <i class="fas fa-hashtag"></i>
                </div>
                <div>
                    <h1 style="margin: 0; font-size: 24px; font-weight: 600;">哈希生成工具</h1>
                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">MD5、SHA1、SHA256等哈希计算</p>
                </div>
            </div>
        </div>
        
        <!-- 输入区域 -->
        <div class="input-section">
            <div class="form-group">
                <label class="form-label" for="input-text">输入文本</label>
                <textarea 
                    class="form-textarea" 
                    id="input-text" 
                    placeholder="输入要计算哈希值的文本内容..."
                ></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label">选择哈希算法</label>
                <div class="hash-options">
                    <label class="hash-option selected" data-algorithm="md5">
                        <input type="checkbox" class="hash-checkbox" checked data-algorithm="md5">
                        <span>MD5</span>
                    </label>
                    <label class="hash-option selected" data-algorithm="sha1">
                        <input type="checkbox" class="hash-checkbox" checked data-algorithm="sha1">
                        <span>SHA1</span>
                    </label>
                    <label class="hash-option selected" data-algorithm="sha256">
                        <input type="checkbox" class="hash-checkbox" checked data-algorithm="sha256">
                        <span>SHA256</span>
                    </label>
                    <label class="hash-option" data-algorithm="sha512">
                        <input type="checkbox" class="hash-checkbox" data-algorithm="sha512">
                        <span>SHA512</span>
                    </label>
                    <label class="hash-option" data-algorithm="sha3">
                        <input type="checkbox" class="hash-checkbox" data-algorithm="sha3">
                        <span>SHA3</span>
                    </label>
                    <label class="hash-option" data-algorithm="ripemd160">
                        <input type="checkbox" class="hash-checkbox" data-algorithm="ripemd160">
                        <span>RIPEMD160</span>
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">快速示例</label>
                <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                    <button type="button" class="action-btn" onclick="fillSampleText('simple')" style="font-size: 12px;">
                        <i class="fas fa-text-width"></i> 简单文本
                    </button>
                    <button type="button" class="action-btn" onclick="fillSampleText('chinese')" style="font-size: 12px;">
                        <i class="fas fa-language"></i> 中文文本
                    </button>
                    <button type="button" class="action-btn" onclick="fillSampleText('json')" style="font-size: 12px;">
                        <i class="fas fa-code"></i> JSON数据
                    </button>
                    <button type="button" class="action-btn" onclick="fillSampleText('password')" style="font-size: 12px;">
                        <i class="fas fa-lock"></i> 密码示例
                    </button>
                </div>
            </div>
            
            <button class="generate-btn" onclick="generateHashes()">
                <i class="fas fa-calculator"></i>
                生成哈希值
            </button>
        </div>
        
        <!-- 结果区域 -->
        <div class="results-section" id="results-section">
            <div class="empty-state">
                <i class="fas fa-hashtag empty-icon"></i>
                <h3 style="margin: 0 0 8px 0; font-size: 18px;">输入文本并选择算法</h3>
                <p style="margin: 0; color: #64748b;">点击生成按钮计算哈希值</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupHashOptions();
        });
        
        // 设置哈希选项
        function setupHashOptions() {
            const options = document.querySelectorAll('.hash-option');
            
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const checkbox = this.querySelector('.hash-checkbox');
                    checkbox.checked = !checkbox.checked;
                    
                    if (checkbox.checked) {
                        this.classList.add('selected');
                    } else {
                        this.classList.remove('selected');
                    }
                });
                
                // 防止点击checkbox时触发两次
                const checkbox = option.querySelector('.hash-checkbox');
                checkbox.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });
        }
        
        // 生成哈希值
        function generateHashes() {
            const inputText = document.getElementById('input-text').value;
            const resultsSection = document.getElementById('results-section');
            
            if (!inputText.trim()) {
                alert('请输入要计算哈希值的文本');
                return;
            }
            
            const selectedAlgorithms = getSelectedAlgorithms();
            
            if (selectedAlgorithms.length === 0) {
                alert('请至少选择一种哈希算法');
                return;
            }
            
            // 清空结果区域
            resultsSection.innerHTML = '';
            
            // 生成各种哈希值
            selectedAlgorithms.forEach(algorithm => {
                const hash = calculateHash(inputText, algorithm);
                const resultItem = createResultItem(algorithm, hash);
                resultsSection.appendChild(resultItem);
            });
        }
        
        // 获取选中的算法
        function getSelectedAlgorithms() {
            const checkboxes = document.querySelectorAll('.hash-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.dataset.algorithm);
        }
        
        // 计算哈希值
        function calculateHash(text, algorithm) {
            switch (algorithm) {
                case 'md5':
                    return CryptoJS.MD5(text).toString();
                case 'sha1':
                    return CryptoJS.SHA1(text).toString();
                case 'sha256':
                    return CryptoJS.SHA256(text).toString();
                case 'sha512':
                    return CryptoJS.SHA512(text).toString();
                case 'sha3':
                    return CryptoJS.SHA3(text).toString();
                case 'ripemd160':
                    return CryptoJS.RIPEMD160(text).toString();
                default:
                    return '';
            }
        }
        
        // 创建结果项
        function createResultItem(algorithm, hash) {
            const div = document.createElement('div');
            div.className = 'result-item';
            
            div.innerHTML = `
                <div class="result-header">
                    <div class="result-title">
                        <i class="fas fa-key"></i>
                        ${algorithm.toUpperCase()}
                    </div>
                    <div class="result-actions">
                        <button class="action-btn" onclick="copyHash('${hash}', this)">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                        <button class="action-btn" onclick="compareHash('${algorithm}', '${hash}')">
                            <i class="fas fa-search"></i> 验证
                        </button>
                    </div>
                </div>
                <div class="result-value">${hash}</div>
            `;
            
            return div;
        }
        
        // 复制哈希值
        async function copyHash(hash, button) {
            try {
                await navigator.clipboard.writeText(hash);
                
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> 已复制';
                button.style.background = '#10b981';
                button.style.color = 'white';
                button.style.borderColor = '#10b981';
                
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.style.background = '';
                    button.style.color = '';
                    button.style.borderColor = '';
                }, 2000);
                
            } catch (error) {
                alert('复制失败');
            }
        }
        
        // 验证哈希值
        function compareHash(algorithm, originalHash) {
            const inputHash = prompt(`请输入要验证的${algorithm.toUpperCase()}哈希值:`);
            
            if (inputHash === null) return;
            
            const cleanInput = inputHash.trim().toLowerCase();
            const cleanOriginal = originalHash.toLowerCase();
            
            if (cleanInput === cleanOriginal) {
                alert('✅ 哈希值匹配！验证通过。');
            } else {
                alert('❌ 哈希值不匹配！验证失败。');
            }
        }
        
        // 快速填充示例文本
        function fillSampleText(type) {
            const textarea = document.getElementById('input-text');
            
            switch (type) {
                case 'simple':
                    textarea.value = 'Hello, World!';
                    break;
                case 'chinese':
                    textarea.value = '你好，世界！这是一个中文测试文本。';
                    break;
                case 'json':
                    textarea.value = '{"name":"张三","age":25,"email":"<EMAIL>"}';
                    break;
                case 'password':
                    textarea.value = 'MySecurePassword123!';
                    break;
            }
        }
    </script>
</body>
</html>
