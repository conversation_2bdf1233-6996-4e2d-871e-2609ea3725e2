<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正则表达式测试工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .tool-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
        }
        
        .tool-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .regex-input-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
        }
        
        .regex-input-group {
            display: flex;
            gap: 12px;
            align-items: end;
            margin-bottom: 16px;
        }
        
        .regex-input-field {
            flex: 1;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .regex-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        .regex-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .regex-input.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .flags-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .flag-option {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #374151;
        }
        
        .flag-checkbox {
            width: 16px;
            height: 16px;
        }
        
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .test-btn:hover {
            background: #2563eb;
        }
        
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            overflow: hidden;
        }
        
        .test-text-section {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8fafc;
            padding: 12px 16px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .section-title {
            font-weight: 500;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .test-textarea {
            flex: 1;
            border: none;
            outline: none;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            background: transparent;
        }
        
        .results-section {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .results-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }
        
        .match-item {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
        }
        
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .match-index {
            font-size: 12px;
            font-weight: 500;
            color: #0369a1;
            background: #e0f2fe;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .match-position {
            font-size: 12px;
            color: #64748b;
        }
        
        .match-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #1e293b;
            background: white;
            padding: 8px 10px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            word-break: break-all;
        }
        
        .groups-section {
            margin-top: 8px;
        }
        
        .group-item {
            background: #fef3c7;
            border: 1px solid #fcd34d;
            border-radius: 6px;
            padding: 6px 8px;
            margin: 4px 0;
            font-size: 12px;
        }
        
        .group-label {
            font-weight: 500;
            color: #92400e;
        }
        
        .group-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            color: #451a03;
        }
        
        .no-matches {
            text-align: center;
            color: #94a3b8;
            padding: 40px 20px;
        }
        
        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 12px;
            color: #dc2626;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .stats-bar {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 8px 16px;
            font-size: 12px;
            color: #64748b;
            display: flex;
            justify-content: space-between;
        }
        
        .highlighted-text {
            background: #fef3c7;
            border-radius: 3px;
            padding: 1px 2px;
        }
        
        .preset-patterns {
            margin-top: 12px;
        }
        
        .preset-title {
            font-size: 12px;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .preset-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        
        .preset-btn {
            padding: 4px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #64748b;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .preset-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="tool-container">
        <!-- 工具头部 -->
        <div class="tool-header">
            <div class="tool-title" style="display: flex; align-items: center; gap: 12px;">
                <div class="tool-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div>
                    <h1 style="margin: 0; font-size: 24px; font-weight: 600;">正则表达式测试工具</h1>
                    <p style="margin: 4px 0 0 0; font-size: 14px; color: #64748b;">正则表达式测试和匹配</p>
                </div>
            </div>
        </div>
        
        <!-- 正则输入区域 -->
        <div class="regex-input-section">
            <div class="regex-input-group">
                <div class="regex-input-field">
                    <label class="form-label">正则表达式</label>
                    <input 
                        type="text" 
                        class="regex-input" 
                        id="regex-input" 
                        placeholder="输入正则表达式..."
                        oninput="testRegex()"
                    >
                </div>
                
                <div class="flags-group">
                    <div class="flag-option">
                        <input type="checkbox" class="flag-checkbox" id="flag-g" checked onchange="testRegex()">
                        <label for="flag-g">g (全局)</label>
                    </div>
                    <div class="flag-option">
                        <input type="checkbox" class="flag-checkbox" id="flag-i" onchange="testRegex()">
                        <label for="flag-i">i (忽略大小写)</label>
                    </div>
                    <div class="flag-option">
                        <input type="checkbox" class="flag-checkbox" id="flag-m" onchange="testRegex()">
                        <label for="flag-m">m (多行)</label>
                    </div>
                </div>
                
                <button class="test-btn" onclick="testRegex()">
                    <i class="fas fa-play"></i> 测试
                </button>
            </div>
            
            <div id="regex-error" class="error-message" style="display: none;"></div>
            
            <!-- 预设模式 -->
            <div class="preset-patterns">
                <div class="preset-title">常用模式</div>
                <div class="preset-buttons">
                    <button class="preset-btn" onclick="setPresetPattern('email')">邮箱</button>
                    <button class="preset-btn" onclick="setPresetPattern('phone')">手机号</button>
                    <button class="preset-btn" onclick="setPresetPattern('url')">网址</button>
                    <button class="preset-btn" onclick="setPresetPattern('ip')">IP地址</button>
                    <button class="preset-btn" onclick="setPresetPattern('date')">日期</button>
                    <button class="preset-btn" onclick="setPresetPattern('number')">数字</button>
                    <button class="preset-btn" onclick="setPresetPattern('chinese')">中文</button>
                    <button class="preset-btn" onclick="setPresetPattern('password')">密码</button>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 测试文本区域 -->
            <div class="test-text-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-file-alt"></i>
                        测试文本
                    </div>
                    <div class="section-actions">
                        <button class="action-btn" onclick="clearTestText()">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                        <button class="action-btn" onclick="loadSampleText()">
                            <i class="fas fa-file-import"></i> 示例
                        </button>
                    </div>
                </div>
                
                <textarea 
                    class="test-textarea" 
                    id="test-text" 
                    placeholder="输入要测试的文本内容..."
                    oninput="testRegex()"
                ></textarea>
                
                <div class="stats-bar">
                    <span id="text-stats">字符: 0 | 行数: 1</span>
                    <span id="match-stats">匹配: 0</span>
                </div>
            </div>
            
            <!-- 匹配结果区域 -->
            <div class="results-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-search"></i>
                        匹配结果
                    </div>
                    <div class="section-actions">
                        <button class="action-btn" onclick="exportResults()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                        <button class="action-btn" onclick="copyAllMatches()">
                            <i class="fas fa-copy"></i> 复制全部
                        </button>
                    </div>
                </div>
                
                <div class="results-content" id="results-content">
                    <div class="no-matches">
                        <i class="fas fa-search" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                        <p>输入正则表达式和测试文本开始匹配</p>
                    </div>
                </div>
                
                <div class="stats-bar">
                    <span id="results-stats">总匹配数: 0</span>
                    <span id="execution-time">执行时间: 0ms</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let lastMatches = [];
        
        // 预设正则模式
        const presetPatterns = {
            email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            phone: /^1[3-9]\d{9}$/,
            url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
            ip: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            date: /^\d{4}-\d{2}-\d{2}$/,
            number: /^-?\d+(\.\d+)?$/,
            chinese: /[\u4e00-\u9fa5]/g,
            password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
        };
        
        // 设置预设模式
        function setPresetPattern(type) {
            const pattern = presetPatterns[type];
            if (pattern) {
                document.getElementById('regex-input').value = pattern.source;
                
                // 设置标志
                document.getElementById('flag-g').checked = pattern.global || false;
                document.getElementById('flag-i').checked = pattern.ignoreCase || false;
                document.getElementById('flag-m').checked = pattern.multiline || false;
                
                testRegex();
            }
        }
        
        // 测试正则表达式
        function testRegex() {
            const regexInput = document.getElementById('regex-input');
            const testText = document.getElementById('test-text').value;
            const regexError = document.getElementById('regex-error');
            const resultsContent = document.getElementById('results-content');
            
            // 清除错误状态
            regexInput.classList.remove('error');
            regexError.style.display = 'none';
            
            // 更新文本统计
            updateTextStats();
            
            if (!regexInput.value.trim()) {
                showNoMatches();
                return;
            }
            
            try {
                const startTime = performance.now();
                
                // 构建正则表达式
                const flags = getFlags();
                const regex = new RegExp(regexInput.value, flags);
                
                // 执行匹配
                const matches = [];
                let match;
                
                if (flags.includes('g')) {
                    while ((match = regex.exec(testText)) !== null) {
                        matches.push({
                            match: match[0],
                            index: match.index,
                            groups: match.slice(1),
                            namedGroups: match.groups || {}
                        });
                        
                        // 防止无限循环
                        if (match[0].length === 0) {
                            regex.lastIndex++;
                        }
                    }
                } else {
                    match = regex.exec(testText);
                    if (match) {
                        matches.push({
                            match: match[0],
                            index: match.index,
                            groups: match.slice(1),
                            namedGroups: match.groups || {}
                        });
                    }
                }
                
                const endTime = performance.now();
                const executionTime = Math.round((endTime - startTime) * 100) / 100;
                
                lastMatches = matches;
                displayResults(matches, executionTime);
                
            } catch (error) {
                regexInput.classList.add('error');
                regexError.textContent = `正则表达式错误: ${error.message}`;
                regexError.style.display = 'block';
                showNoMatches();
            }
        }
        
        // 获取标志
        function getFlags() {
            let flags = '';
            if (document.getElementById('flag-g').checked) flags += 'g';
            if (document.getElementById('flag-i').checked) flags += 'i';
            if (document.getElementById('flag-m').checked) flags += 'm';
            return flags;
        }
        
        // 显示结果
        function displayResults(matches, executionTime) {
            const resultsContent = document.getElementById('results-content');
            
            if (matches.length === 0) {
                showNoMatches();
                return;
            }
            
            resultsContent.innerHTML = '';
            
            matches.forEach((match, index) => {
                const matchItem = createMatchItem(match, index);
                resultsContent.appendChild(matchItem);
            });
            
            // 更新统计
            document.getElementById('match-stats').textContent = `匹配: ${matches.length}`;
            document.getElementById('results-stats').textContent = `总匹配数: ${matches.length}`;
            document.getElementById('execution-time').textContent = `执行时间: ${executionTime}ms`;
        }
        
        // 创建匹配项
        function createMatchItem(match, index) {
            const div = document.createElement('div');
            div.className = 'match-item';
            
            let groupsHtml = '';
            if (match.groups.length > 0) {
                groupsHtml = '<div class="groups-section">';
                match.groups.forEach((group, groupIndex) => {
                    if (group !== undefined) {
                        groupsHtml += `
                            <div class="group-item">
                                <span class="group-label">组 ${groupIndex + 1}:</span>
                                <span class="group-value">${escapeHtml(group)}</span>
                            </div>
                        `;
                    }
                });
                
                // 命名组
                Object.entries(match.namedGroups).forEach(([name, value]) => {
                    if (value !== undefined) {
                        groupsHtml += `
                            <div class="group-item">
                                <span class="group-label">${name}:</span>
                                <span class="group-value">${escapeHtml(value)}</span>
                            </div>
                        `;
                    }
                });
                
                groupsHtml += '</div>';
            }
            
            div.innerHTML = `
                <div class="match-header">
                    <span class="match-index">匹配 ${index + 1}</span>
                    <span class="match-position">位置: ${match.index}-${match.index + match.match.length}</span>
                </div>
                <div class="match-value">${escapeHtml(match.match)}</div>
                ${groupsHtml}
            `;
            
            return div;
        }
        
        // 显示无匹配
        function showNoMatches() {
            const resultsContent = document.getElementById('results-content');
            resultsContent.innerHTML = `
                <div class="no-matches">
                    <i class="fas fa-times-circle" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3; color: #ef4444;"></i>
                    <p>没有找到匹配项</p>
                </div>
            `;
            
            document.getElementById('match-stats').textContent = '匹配: 0';
            document.getElementById('results-stats').textContent = '总匹配数: 0';
            document.getElementById('execution-time').textContent = '执行时间: 0ms';
        }
        
        // 更新文本统计
        function updateTextStats() {
            const testText = document.getElementById('test-text').value;
            const lines = testText.split('\n').length;
            const chars = testText.length;
            
            document.getElementById('text-stats').textContent = `字符: ${chars} | 行数: ${lines}`;
        }
        
        // 清空测试文本
        function clearTestText() {
            document.getElementById('test-text').value = '';
            testRegex();
        }
        
        // 加载示例文本
        function loadSampleText() {
            const sampleText = `张三的邮箱是 <EMAIL>，手机号是 13812345678。
李四的邮箱是 <EMAIL>，手机号是 15987654321。
网站地址：https://www.example.com
IP地址：***********
日期：2024-01-15
价格：￥299.99
密码：MyPassword123!`;
            
            document.getElementById('test-text').value = sampleText;
            testRegex();
        }
        
        // 复制所有匹配
        async function copyAllMatches() {
            if (lastMatches.length === 0) {
                alert('没有匹配结果可复制');
                return;
            }
            
            const matchTexts = lastMatches.map(match => match.match).join('\n');
            
            try {
                await navigator.clipboard.writeText(matchTexts);
                
                const button = event.target.closest('.action-btn');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> 已复制';
                
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                }, 2000);
                
            } catch (error) {
                alert('复制失败');
            }
        }
        
        // 导出结果
        function exportResults() {
            if (lastMatches.length === 0) {
                alert('没有匹配结果可导出');
                return;
            }
            
            const regex = document.getElementById('regex-input').value;
            const flags = getFlags();
            const timestamp = new Date().toLocaleString();
            
            let content = `正则表达式测试结果\n`;
            content += `生成时间: ${timestamp}\n`;
            content += `正则表达式: /${regex}/${flags}\n`;
            content += `匹配数量: ${lastMatches.length}\n\n`;
            
            lastMatches.forEach((match, index) => {
                content += `匹配 ${index + 1}:\n`;
                content += `  内容: ${match.match}\n`;
                content += `  位置: ${match.index}-${match.index + match.match.length}\n`;
                
                if (match.groups.length > 0) {
                    content += `  分组:\n`;
                    match.groups.forEach((group, groupIndex) => {
                        if (group !== undefined) {
                            content += `    组 ${groupIndex + 1}: ${group}\n`;
                        }
                    });
                }
                
                content += '\n';
            });
            
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `regex-test-results-${new Date().getTime()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTextStats();
        });
    </script>
</body>
</html>
