// 主题变量定义
// 颜色系统
@primary-color: #3b82f6;
@success-color: #10b981;
@warning-color: #f59e0b;
@error-color: #ef4444;
@info-color: #06b6d4;

// 中性色
@text-color: #1f2937;
@text-color-secondary: #64748b;
@text-color-disabled: #9ca3af;
@border-color: #e2e8f0;
@border-color-light: #f1f5f9;
@background-color: #ffffff;
@background-color-light: #f8fafc;
@background-color-dark: #f1f5f9;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 12px;
@spacing-lg: 16px;
@spacing-xl: 20px;
@spacing-xxl: 24px;

// 圆角
@border-radius-sm: 4px;
@border-radius-md: 8px;
@border-radius-lg: 12px;
@border-radius-xl: 16px;

// 阴影
@box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
@box-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
@box-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
@box-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

// 字体
@font-size-xs: 12px;
@font-size-sm: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 20px;
@font-size-xxl: 24px;

// 布局
@sidebar-width: 280px;
@titlebar-height: 48px;
@tab-height: 48px;

// 动画
@transition-fast: 0.15s;
@transition-normal: 0.2s;
@transition-slow: 0.3s;

// 渐变
@gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
@gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

// Z-index层级
@z-index-dropdown: 1000;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;
@z-index-notification: 1080;
