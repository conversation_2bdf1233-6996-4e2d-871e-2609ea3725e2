{"$schema": "https://schema.tauri.app/config/2", "identifier": "com.mytoolkit.app", "productName": "MyToolkit", "version": "1.0.0", "build": {"devUrl": "http://localhost:5173", "frontendDist": "../dist", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "MyToolkit - 工具箱", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "transparent": false, "center": true, "focus": true, "maximized": false, "visible": true, "skipTaskbar": false, "alwaysOnTop": false, "contentProtected": false, "shadow": true, "titleBarStyle": "Visible"}], "security": {"csp": "default-src 'self'; style-src 'self' 'unsafe-inline'; font-src 'self' data:; img-src 'self' data: https:; script-src 'self' 'unsafe-eval'"}}, "bundle": {"active": true, "targets": "all", "publisher": "MyToolkit Team", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": [], "copyright": "Copyright © 2024 MyToolkit Team", "category": "Utility", "shortDescription": "个人工具箱桌面应用", "longDescription": "一个提供个人常用小工具集合的桌面应用程序，包括二维码解析、文本格式化等功能。", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.15", "exceptionDomain": ""}}}