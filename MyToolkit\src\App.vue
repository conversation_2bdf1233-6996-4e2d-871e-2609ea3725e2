<template>
  <div id="app" class="app-container">
    <!-- 标题栏 -->
    <AppTitleBar />

    <!-- 主内容区 -->
    <div class="app-content">
      <router-view />
    </div>

    <!-- 全局加载状态 -->
    <div v-if="appStore.isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <LoadingOutlined :spin="true" />
        <span class="loading-text">正在加载...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import AppTitleBar from '@/components/AppTitleBar.vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

onMounted(async () => {
  // 初始化应用
  await appStore.initializeApp()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const handleResize = () => {
  // 处理响应式布局
  const width = window.innerWidth
  if (width < 768) {
    // 移动端适配
    console.log('切换到移动端布局')
  } else {
    // 桌面端布局
    console.log('切换到桌面端布局')
  }
}
</script>

<style lang="less">
@import '@/assets/styles/global.less';

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: @background-color-light;
  overflow: hidden;
  position: relative;
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: @z-index-modal;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: @spacing-lg;
  padding: @spacing-xxl;
  background: @background-color;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-lg;

  .anticon {
    font-size: @font-size-xxl;
    color: @primary-color;
  }
}

.loading-text {
  font-size: @font-size-sm;
  color: @text-color-secondary;
}

// 响应式适配
@media (max-width: 768px) {
  .app-container {
    .app-content {
      flex-direction: column;
    }
  }
}
</style>
