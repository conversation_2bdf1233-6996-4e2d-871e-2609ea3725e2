<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具箱 - 桌面应用</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .app-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 1400px;
            margin: 0 auto;
            height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
        }
        
        .title-bar {
            background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 12px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .window-controls {
            display: flex;
            gap: 8px;
        }
        
        .control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
        }
        
        .close { background: #ff5f57; }
        .minimize { background: #ffbd2e; }
        .maximize { background: #28ca42; }
        
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .sidebar-container {
            width: 280px;
            border-right: 1px solid #e2e8f0;
            background: #f8fafc;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .tab-bar {
            background: #f1f5f9;
            border-bottom: 1px solid #e2e8f0;
            padding: 0 20px;
            display: flex;
            align-items: center;
            min-height: 48px;
            overflow-x: auto;
        }
        
        .tab {
            background: white;
            border: 1px solid #e2e8f0;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            padding: 8px 16px;
            margin-right: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
            transition: all 0.2s;
        }
        
        .tab.active {
            background: white;
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .tab:hover {
            background: #f8fafc;
        }
        
        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #e2e8f0;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #64748b;
        }
        
        .tab-close:hover {
            background: #ef4444;
            color: white;
        }
        
        .tool-content {
            flex: 1;
            overflow: hidden;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #64748b;
            text-align: center;
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 标题栏 -->
        <div class="title-bar">
            <div class="window-controls">
                <button class="control-btn close"></button>
                <button class="control-btn minimize"></button>
                <button class="control-btn maximize"></button>
            </div>
            <div class="flex items-center gap-3">
                <i class="fas fa-toolbox text-blue-600"></i>
                <span class="font-semibold text-gray-800">工具箱</span>
            </div>
            <div class="text-sm text-gray-500">v1.0.0</div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar-container">
                <iframe id="sidebar-frame" src="sidebar.html"></iframe>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <!-- Tab栏 -->
                <div class="tab-bar" id="tab-bar">
                    <!-- Tabs will be dynamically added here -->
                </div>
                
                <!-- 工具内容 -->
                <div class="tool-content" id="tool-content">
                    <div class="empty-state">
                        <i class="fas fa-mouse-pointer empty-icon"></i>
                        <h3 class="text-xl font-semibold mb-2">选择一个工具开始使用</h3>
                        <p class="text-gray-500">从左侧工具列表中选择您需要的工具</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 工具管理
        class ToolManager {
            constructor() {
                this.openTools = new Map();
                this.activeToolId = null;
                this.tabBar = document.getElementById('tab-bar');
                this.toolContent = document.getElementById('tool-content');
                
                // 监听来自sidebar的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'openTool') {
                        this.openTool(event.data.toolId, event.data.toolName, event.data.toolUrl);
                    }
                });
            }
            
            openTool(toolId, toolName, toolUrl) {
                if (this.openTools.has(toolId)) {
                    // 工具已打开，切换到该工具
                    this.switchToTool(toolId);
                } else {
                    // 打开新工具
                    this.createNewTool(toolId, toolName, toolUrl);
                }
            }
            
            createNewTool(toolId, toolName, toolUrl) {
                // 创建tab
                const tab = document.createElement('div');
                tab.className = 'tab';
                tab.dataset.toolId = toolId;
                tab.innerHTML = `
                    <i class="fas fa-qrcode"></i>
                    <span>${toolName}</span>
                    <button class="tab-close" onclick="toolManager.closeTool('${toolId}')">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                tab.addEventListener('click', (e) => {
                    if (!e.target.closest('.tab-close')) {
                        this.switchToTool(toolId);
                    }
                });
                
                // 创建iframe
                const iframe = document.createElement('iframe');
                iframe.src = toolUrl;
                iframe.style.display = 'none';
                
                // 存储工具信息
                this.openTools.set(toolId, {
                    tab: tab,
                    iframe: iframe,
                    name: toolName,
                    url: toolUrl
                });
                
                // 添加到DOM
                this.tabBar.appendChild(tab);
                this.toolContent.appendChild(iframe);
                
                // 切换到新工具
                this.switchToTool(toolId);
            }
            
            switchToTool(toolId) {
                // 隐藏所有工具
                this.openTools.forEach((tool, id) => {
                    tool.tab.classList.remove('active');
                    tool.iframe.style.display = 'none';
                });
                
                // 显示目标工具
                const tool = this.openTools.get(toolId);
                if (tool) {
                    tool.tab.classList.add('active');
                    tool.iframe.style.display = 'block';
                    this.activeToolId = toolId;
                    
                    // 隐藏空状态
                    const emptyState = this.toolContent.querySelector('.empty-state');
                    if (emptyState) {
                        emptyState.style.display = 'none';
                    }
                }
            }
            
            closeTool(toolId) {
                const tool = this.openTools.get(toolId);
                if (tool) {
                    // 移除DOM元素
                    tool.tab.remove();
                    tool.iframe.remove();
                    
                    // 从Map中删除
                    this.openTools.delete(toolId);
                    
                    // 如果关闭的是当前活动工具
                    if (this.activeToolId === toolId) {
                        this.activeToolId = null;
                        
                        // 切换到其他工具或显示空状态
                        if (this.openTools.size > 0) {
                            const firstToolId = this.openTools.keys().next().value;
                            this.switchToTool(firstToolId);
                        } else {
                            const emptyState = this.toolContent.querySelector('.empty-state');
                            if (emptyState) {
                                emptyState.style.display = 'flex';
                            }
                        }
                    }
                }
            }
        }
        
        // 初始化工具管理器
        const toolManager = new ToolManager();
    </script>
</body>
</html>
